#!/bin/bash

# Docker镜像构建和部署脚本
# 功能：自动化构建、测试和部署视频备份系统Docker镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
IMAGE_NAME="video-backup"
IMAGE_TAG="latest"
CONTAINER_NAME="video-backup-system"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
视频备份系统 Docker 构建脚本

用法: $0 [命令] [选项]

命令:
    build       构建Docker镜像
    test        测试Docker镜像
    run         运行容器
    stop        停止容器
    clean       清理镜像和容器
    deploy      完整部署 (构建+测试+运行)
    help        显示帮助信息

选项:
    --tag TAG   指定镜像标签 (默认: latest)
    --name NAME 指定容器名称 (默认: video-backup-system)
    --no-cache  构建时不使用缓存

示例:
    $0 build                    # 构建镜像
    $0 build --tag v1.0        # 构建指定标签的镜像
    $0 test                     # 测试镜像
    $0 deploy                   # 完整部署
    $0 clean                    # 清理资源

EOF
}

# 检查Docker环境
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行或权限不足"
        exit 1
    fi
    
    log_info "Docker环境检查通过"
}

# 检查必要文件
check_files() {
    local required_files=(
        "Dockerfile"
        "docker-entrypoint.sh"
        "../scripts/video_backup_sync.sh"
        "../scripts/video_cleanup.sh"
        "../scripts/monitor_system.sh"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    log_info "必要文件检查通过"
}

# 构建Docker镜像
build_image() {
    local no_cache=""
    if [[ "$1" == "--no-cache" ]]; then
        no_cache="--no-cache"
        log_info "使用无缓存构建"
    fi
    
    log_info "开始构建Docker镜像: ${IMAGE_NAME}:${IMAGE_TAG}"
    
    docker build $no_cache -t "${IMAGE_NAME}:${IMAGE_TAG}" .
    
    if [[ $? -eq 0 ]]; then
        log_info "✅ 镜像构建成功"
        
        # 显示镜像信息
        local image_size=$(docker images "${IMAGE_NAME}:${IMAGE_TAG}" --format "{{.Size}}")
        log_info "镜像大小: $image_size"
    else
        log_error "❌ 镜像构建失败"
        exit 1
    fi
}

# 测试Docker镜像
test_image() {
    log_info "开始测试Docker镜像..."
    
    # 检查镜像是否存在
    if ! docker images "${IMAGE_NAME}:${IMAGE_TAG}" | grep -q "${IMAGE_TAG}"; then
        log_error "镜像不存在: ${IMAGE_NAME}:${IMAGE_TAG}"
        log_info "请先运行构建命令: $0 build"
        exit 1
    fi
    
    # 测试容器启动
    log_info "测试容器启动..."
    local test_container="${CONTAINER_NAME}-test"
    
    # 清理可能存在的测试容器
    docker rm -f "$test_container" &> /dev/null || true
    
    # 启动测试容器
    docker run -d \
        --name "$test_container" \
        -e MINIO_URL=http://test.example.com:9000 \
        -e MINIO_ACCESS_KEY=testkey \
        -e MINIO_SECRET_KEY=testsecret \
        "${IMAGE_NAME}:${IMAGE_TAG}" help
    
    # 等待容器启动
    sleep 5
    
    # 检查容器状态
    if docker ps -a --filter "name=$test_container" --format "{{.Status}}" | grep -q "Exited (0)"; then
        log_info "✅ 容器启动测试通过"
    else
        log_error "❌ 容器启动测试失败"
        docker logs "$test_container"
        docker rm -f "$test_container"
        exit 1
    fi
    
    # 清理测试容器
    docker rm -f "$test_container"
    
    log_info "✅ 镜像测试完成"
}

# 运行容器
run_container() {
    log_info "启动视频备份容器..."
    
    # 检查环境变量文件
    if [[ ! -f ".env" ]]; then
        log_warn ".env文件不存在，使用默认配置"
        if [[ -f "../config/.env.template" ]]; then
            log_info "复制模板文件..."
            cp ../config/.env.template .env
            log_warn "请编辑 .env 文件设置正确的MinIO配置"
        fi
    fi
    
    # 停止现有容器
    docker rm -f "$CONTAINER_NAME" &> /dev/null || true
    
    # 创建必要目录
    mkdir -p ../backup ../logs ../config
    
    # 启动容器
    if [[ -f "docker-compose.yml" ]]; then
        log_info "使用docker-compose启动..."
        docker-compose up -d
    else
        log_info "使用docker run启动..."
        docker run -d \
            --name "$CONTAINER_NAME" \
            --restart unless-stopped \
            --env-file .env \
            -v "$(pwd)/../backup:/app/backup" \
            -v "$(pwd)/../logs:/app/logs" \
            -v "$(pwd)/../config:/app/config" \
            "${IMAGE_NAME}:${IMAGE_TAG}"
    fi
    
    # 等待容器启动
    sleep 10
    
    # 检查容器状态
    if docker ps --filter "name=$CONTAINER_NAME" | grep -q "$CONTAINER_NAME"; then
        log_info "✅ 容器启动成功"
        log_info "容器名称: $CONTAINER_NAME"
        log_info "查看日志: docker logs -f $CONTAINER_NAME"
    else
        log_error "❌ 容器启动失败"
        docker logs "$CONTAINER_NAME"
        exit 1
    fi
}

# 停止容器
stop_container() {
    log_info "停止视频备份容器..."
    
    if [[ -f "docker-compose.yml" ]]; then
        docker-compose down
    else
        docker stop "$CONTAINER_NAME" &> /dev/null || true
        docker rm "$CONTAINER_NAME" &> /dev/null || true
    fi
    
    log_info "✅ 容器已停止"
}

# 清理资源
clean_resources() {
    log_info "清理Docker资源..."
    
    # 停止并删除容器
    stop_container
    
    # 删除镜像
    docker rmi "${IMAGE_NAME}:${IMAGE_TAG}" &> /dev/null || true
    
    # 清理未使用的资源
    docker system prune -f
    
    log_info "✅ 资源清理完成"
}

# 完整部署
deploy() {
    log_info "开始完整部署..."
    
    check_docker
    check_files
    build_image
    test_image
    run_container
    
    log_info "🎉 部署完成！"
    log_info "管理命令："
    log_info "  查看状态: docker ps"
    log_info "  查看日志: docker logs -f $CONTAINER_NAME"
    log_info "  进入容器: docker exec -it $CONTAINER_NAME /bin/bash"
    log_info "  停止服务: $0 stop"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            --name)
                CONTAINER_NAME="$2"
                shift 2
                ;;
            --no-cache)
                NO_CACHE="--no-cache"
                shift
                ;;
            *)
                break
                ;;
        esac
    done
}

# 主函数
main() {
    local command=${1:-help}
    shift || true
    
    # 解析参数
    parse_args "$@"
    
    case $command in
        "build")
            check_docker
            check_files
            build_image $NO_CACHE
            ;;
        "test")
            check_docker
            test_image
            ;;
        "run")
            check_docker
            run_container
            ;;
        "stop")
            stop_container
            ;;
        "clean")
            clean_resources
            ;;
        "deploy")
            deploy
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
