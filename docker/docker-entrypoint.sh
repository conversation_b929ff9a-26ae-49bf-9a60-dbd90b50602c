#!/bin/bash

# Docker容器入口脚本
# 功能：初始化容器环境并启动视频备份服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR]${NC} $1"
}

# 初始化配置
init_config() {
    log_info "初始化配置..."
    
    # 检查必要的环境变量
    if [[ -z "$MINIO_URL" || -z "$MINIO_ACCESS_KEY" || -z "$MINIO_SECRET_KEY" ]]; then
        log_error "缺少必要的环境变量："
        log_error "  MINIO_URL: MinIO服务器地址"
        log_error "  MINIO_ACCESS_KEY: MinIO访问密钥"
        log_error "  MINIO_SECRET_KEY: MinIO密钥"
        exit 1
    fi
    
    # 创建rclone配置
    log_info "创建rclone配置..."
    mkdir -p ~/.config/rclone
    
    cat > ~/.config/rclone/rclone.conf << EOF
[minio-remote]
type = s3
provider = Minio
access_key_id = $MINIO_ACCESS_KEY
secret_access_key = $MINIO_SECRET_KEY
endpoint = $MINIO_URL
region = us-east-1
location_constraint = 
server_side_encryption = 
storage_class = 

[local-backup]
type = local
EOF
    
    log_info "rclone配置创建完成"
}

# 测试连接
test_connection() {
    log_info "测试MinIO连接..."
    
    if rclone lsd minio-remote:records &> /dev/null; then
        log_info "✅ MinIO连接测试成功"
    else
        log_warn "❌ MinIO连接测试失败，请检查配置"
        log_warn "将在后续运行中重试连接"
    fi
}

# 更新脚本中的路径
update_script_paths() {
    log_info "更新脚本路径配置..."
    
    # 更新备份脚本中的路径
    sed -i "s|RCLONE_CONFIG=\"./rclone.conf\"|RCLONE_CONFIG=\"$HOME/.config/rclone/rclone.conf\"|g" /app/video_backup_sync.sh
    sed -i "s|LOCAL_BACKUP_PATH=\"/backup/video_archive\"|LOCAL_BACKUP_PATH=\"${BACKUP_PATH:-/app/backup}\"|g" /app/video_backup_sync.sh
    sed -i "s|LOG_FILE=\"/var/log/video_backup.log\"|LOG_FILE=\"${LOG_PATH:-/app/logs}/video_backup.log\"|g" /app/video_backup_sync.sh
    
    # 更新清理脚本中的路径
    sed -i "s|RCLONE_CONFIG=\"./rclone.conf\"|RCLONE_CONFIG=\"$HOME/.config/rclone/rclone.conf\"|g" /app/video_cleanup.sh
    sed -i "s|LOCAL_BACKUP_PATH=\"/backup/video_archive\"|LOCAL_BACKUP_PATH=\"${BACKUP_PATH:-/app/backup}\"|g" /app/video_cleanup.sh
    sed -i "s|LOG_FILE=\"/var/log/video_cleanup.log\"|LOG_FILE=\"${LOG_PATH:-/app/logs}/video_cleanup.log\"|g" /app/video_cleanup.sh
    
    # 更新监控脚本中的路径
    sed -i "s|RCLONE_CONFIG=\"./rclone.conf\"|RCLONE_CONFIG=\"$HOME/.config/rclone/rclone.conf\"|g" /app/monitor_system.sh
    sed -i "s|LOCAL_BACKUP_PATH=\"/backup/video_archive\"|LOCAL_BACKUP_PATH=\"${BACKUP_PATH:-/app/backup}\"|g" /app/monitor_system.sh
    sed -i "s|LOG_FILE=\"/var/log/video_monitor.log\"|LOG_FILE=\"${LOG_PATH:-/app/logs}/video_monitor.log\"|g" /app/monitor_system.sh
    
    log_info "脚本路径更新完成"
}

# 启动定时任务守护进程
start_daemon() {
    log_info "启动视频备份守护进程..."
    
    # 设置默认的定时间隔（分钟）
    local backup_interval=${BACKUP_INTERVAL:-1440}  # 默认24小时
    local cleanup_interval=${CLEANUP_INTERVAL:-1440}  # 默认24小时
    local monitor_interval=${MONITOR_INTERVAL:-30}    # 默认30分钟
    
    log_info "定时任务配置："
    log_info "  备份间隔: ${backup_interval} 分钟"
    log_info "  清理间隔: ${cleanup_interval} 分钟"
    log_info "  监控间隔: ${monitor_interval} 分钟"
    
    # 后台运行定时任务
    while true; do
        # 备份任务
        if [[ $(($(date +%s) / 60 % backup_interval)) -eq 0 ]]; then
            log_info "执行备份任务..."
            /app/video_backup_sync.sh &
        fi
        
        # 清理任务
        if [[ $(($(date +%s) / 60 % cleanup_interval)) -eq 0 ]]; then
            log_info "执行清理任务..."
            /app/video_cleanup.sh remote &
        fi
        
        # 监控任务
        if [[ $(($(date +%s) / 60 % monitor_interval)) -eq 0 ]]; then
            /app/monitor_system.sh check &
        fi
        
        sleep 60  # 每分钟检查一次
    done
}

# 运行单次任务
run_single_task() {
    local task=$1
    
    case $task in
        "backup")
            log_info "执行单次备份任务..."
            /app/video_backup_sync.sh
            ;;
        "cleanup")
            log_info "执行单次清理任务..."
            /app/video_cleanup.sh remote
            ;;
        "cleanup-local")
            log_info "执行本地清理任务..."
            /app/video_cleanup.sh local
            ;;
        "monitor")
            log_info "执行监控检查..."
            /app/monitor_system.sh check
            ;;
        "status")
            log_info "生成状态报告..."
            /app/monitor_system.sh report
            ;;
        *)
            log_error "未知任务: $task"
            log_error "支持的任务: backup, cleanup, cleanup-local, monitor, status"
            exit 1
            ;;
    esac
}

# 显示帮助信息
show_help() {
    cat << EOF
视频备份系统 Docker 容器

用法: docker run [选项] video-backup [命令]

命令:
    daemon              启动守护进程模式 (默认)
    backup              执行单次备份
    cleanup             执行单次远程清理
    cleanup-local       执行单次本地清理
    monitor             执行监控检查
    status              显示状态报告
    help                显示此帮助

环境变量:
    MINIO_URL           MinIO服务器地址 (必需)
    MINIO_ACCESS_KEY    MinIO访问密钥 (必需)
    MINIO_SECRET_KEY    MinIO密钥 (必需)
    BACKUP_PATH         本地备份路径 (默认: /app/backup)
    LOG_PATH            日志路径 (默认: /app/logs)
    BACKUP_INTERVAL     备份间隔分钟数 (默认: 1440)
    CLEANUP_INTERVAL    清理间隔分钟数 (默认: 1440)
    MONITOR_INTERVAL    监控间隔分钟数 (默认: 30)
    DAYS_TO_KEEP        服务器保留天数 (默认: 7)

示例:
    # 守护进程模式
    docker run -d video-backup daemon
    
    # 单次备份
    docker run --rm video-backup backup
    
    # 查看状态
    docker run --rm video-backup status

EOF
}

# 主函数
main() {
    local command=${1:-daemon}
    
    log_info "视频备份系统容器启动..."
    log_info "命令: $command"
    
    # 初始化
    init_config
    update_script_paths
    test_connection
    
    case $command in
        "daemon")
            start_daemon
            ;;
        "backup"|"cleanup"|"cleanup-local"|"monitor"|"status")
            run_single_task $command
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 信号处理
trap 'log_info "收到停止信号，正在关闭..."; exit 0' SIGTERM SIGINT

# 执行主函数
main "$@"
