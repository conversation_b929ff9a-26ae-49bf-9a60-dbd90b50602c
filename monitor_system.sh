#!/bin/bash

# 视频备份系统监控脚本
# 功能：监控备份系统状态，发送告警和生成报告
# 作者：自动生成

# 配置参数
RCLONE_CONFIG="./rclone.conf"
REMOTE_NAME="minio-remote"
LOCAL_BACKUP_PATH="/backup/video_archive"
BUCKET_NAME="records"
LOG_FILE="/var/log/video_monitor.log"
ALERT_EMAIL=""  # 设置告警邮箱地址
DISK_USAGE_THRESHOLD=85  # 磁盘使用率告警阈值

# 创建日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 发送告警邮件（需要配置邮件系统）
send_alert() {
    local subject=$1
    local message=$2
    
    if [ -n "$ALERT_EMAIL" ]; then
        echo "$message" | mail -s "$subject" "$ALERT_EMAIL"
        log_message "告警邮件已发送: $subject"
    else
        log_message "告警: $subject - $message"
    fi
}

# 检查rclone连接
check_rclone_connection() {
    log_message "检查rclone连接状态..."
    
    if rclone --config="$RCLONE_CONFIG" lsd "$REMOTE_NAME:$BUCKET_NAME" > /dev/null 2>&1; then
        log_message "rclone连接正常"
        return 0
    else
        log_message "rclone连接失败"
        send_alert "视频备份系统告警" "rclone无法连接到远程MinIO服务器"
        return 1
    fi
}

# 检查磁盘空间
check_disk_space() {
    log_message "检查磁盘空间..."
    
    # 检查备份目录所在磁盘的使用率
    if [ -d "$LOCAL_BACKUP_PATH" ]; then
        usage=$(df "$(dirname "$LOCAL_BACKUP_PATH")" | awk 'NR==2 {print $5}' | sed 's/%//')
        
        if [ "$usage" -gt "$DISK_USAGE_THRESHOLD" ]; then
            log_message "磁盘空间不足: ${usage}%"
            send_alert "磁盘空间告警" "备份磁盘使用率已达到 ${usage}%，超过阈值 ${DISK_USAGE_THRESHOLD}%"
            return 1
        else
            log_message "磁盘空间正常: ${usage}%"
            return 0
        fi
    else
        log_message "备份目录不存在: $LOCAL_BACKUP_PATH"
        return 1
    fi
}

# 检查备份任务状态
check_backup_status() {
    log_message "检查备份任务状态..."
    
    local backup_log="/var/log/video_backup.log"
    local last_backup=""
    
    if [ -f "$backup_log" ]; then
        # 查找最近的备份完成记录
        last_backup=$(grep "视频备份同步任务完成" "$backup_log" | tail -1 | cut -d' ' -f1-2)
        
        if [ -n "$last_backup" ]; then
            # 计算距离上次备份的时间
            last_backup_timestamp=$(date -d "$last_backup" +%s 2>/dev/null || echo 0)
            current_timestamp=$(date +%s)
            hours_since_backup=$(( (current_timestamp - last_backup_timestamp) / 3600 ))
            
            if [ "$hours_since_backup" -gt 25 ]; then  # 超过25小时没有备份
                log_message "备份任务异常: 距离上次备份已超过 $hours_since_backup 小时"
                send_alert "备份任务告警" "视频备份任务可能异常，距离上次成功备份已超过 $hours_since_backup 小时"
                return 1
            else
                log_message "备份任务正常: 上次备份时间 $last_backup"
                return 0
            fi
        else
            log_message "未找到备份完成记录"
            return 1
        fi
    else
        log_message "备份日志文件不存在: $backup_log"
        return 1
    fi
}

# 生成系统状态报告
generate_status_report() {
    log_message "========== 系统状态报告 =========="
    
    # 远程存储状态
    if check_rclone_connection; then
        remote_size=$(rclone --config="$RCLONE_CONFIG" size "$REMOTE_NAME:$BUCKET_NAME" --json 2>/dev/null | jq -r '.bytes // 0')
        remote_count=$(rclone --config="$RCLONE_CONFIG" size "$REMOTE_NAME:$BUCKET_NAME" --json 2>/dev/null | jq -r '.count // 0')
        
        if [ "$remote_size" != "0" ]; then
            remote_size_human=$(numfmt --to=iec "$remote_size")
            log_message "远程存储: $remote_size_human ($remote_count 个文件)"
        fi
        
        # 设备数量
        device_count=$(rclone --config="$RCLONE_CONFIG" lsd "$REMOTE_NAME:$BUCKET_NAME" 2>/dev/null | grep -c "device" || echo 0)
        log_message "监控设备数量: $device_count"
    fi
    
    # 本地存储状态
    if [ -d "$LOCAL_BACKUP_PATH" ]; then
        local_usage=$(du -sh "$LOCAL_BACKUP_PATH" 2>/dev/null | cut -f1)
        local_file_count=$(find "$LOCAL_BACKUP_PATH" -type f 2>/dev/null | wc -l)
        log_message "本地备份: $local_usage ($local_file_count 个文件)"
        
        # 最新和最旧的备份
        newest_backup=$(find "$LOCAL_BACKUP_PATH" -name "*_hls" -type d 2>/dev/null | sort | tail -1 | xargs basename 2>/dev/null || echo "无")
        oldest_backup=$(find "$LOCAL_BACKUP_PATH" -name "*_hls" -type d 2>/dev/null | sort | head -1 | xargs basename 2>/dev/null || echo "无")
        log_message "最新备份: $newest_backup"
        log_message "最旧备份: $oldest_backup"
    fi
    
    # 系统资源状态
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//' 2>/dev/null || echo "N/A")
    memory_usage=$(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}' 2>/dev/null || echo "N/A")
    log_message "CPU使用率: $cpu_usage"
    log_message "内存使用率: $memory_usage"
    
    log_message "========== 状态报告结束 =========="
}

# 主函数
main() {
    local mode=${1:-"check"}
    
    log_message "========== 开始系统监控 (模式: $mode) =========="
    
    case $mode in
        "check")
            # 执行所有检查
            connection_ok=0
            disk_ok=0
            backup_ok=0
            
            check_rclone_connection && connection_ok=1
            check_disk_space && disk_ok=1
            check_backup_status && backup_ok=1
            
            if [ $connection_ok -eq 1 ] && [ $disk_ok -eq 1 ] && [ $backup_ok -eq 1 ]; then
                log_message "所有检查通过，系统运行正常"
            else
                log_message "系统检查发现问题，请查看上述日志"
            fi
            ;;
        "report")
            generate_status_report
            ;;
        *)
            log_message "错误: 未知的监控模式: $mode"
            log_message "支持的模式: check, report"
            exit 1
            ;;
    esac
    
    log_message "========== 系统监控完成 =========="
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 [模式]"
    echo "模式选项:"
    echo "  check  - 执行系统健康检查 (默认)"
    echo "  report - 生成详细状态报告"
    echo ""
    echo "配置告警邮箱请编辑脚本中的 ALERT_EMAIL 变量"
}

# 参数处理
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
fi

# 错误处理
set -e
trap 'log_message "监控脚本执行出错，退出码: $?"' ERR

# 执行主函数
main "$@"
