# 视频备份自动化系统

基于rclone的MinIO视频文件自动备份和管理系统，实现服务器视频文件的定期备份和清理。

## 功能特性

- ✅ **自动备份**: 将MinIO服务器上超过7天的视频文件自动备份到本地
- ✅ **智能清理**: 自动删除服务器上已备份的旧文件，节省服务器空间
- ✅ **数据安全**: 备份前验证完整性，确保数据不丢失
- ✅ **定时运行**: 通过cron实现全自动运行，无需人工干预
- ✅ **监控告警**: 系统状态监控和异常告警
- ✅ **详细日志**: 完整的操作日志记录

## 系统架构

```
MinIO服务器 (保留7天)     →     本地备份服务器 (长期存储)
records/                        /backup/video_archive/
├── device[ID]/                 ├── device[ID]/
│   ├── 2025-01-23_*_hls/      │   ├── 2025-01-16_*_hls/
│   └── 2025-01-24_*_hls/      │   └── 2025-01-17_*_hls/
└── ...                         └── ...
```

## 安装要求

### 系统要求
- Linux系统 (Ubuntu/CentOS/Debian等) 或 macOS
- Bash shell
- 足够的磁盘空间用于备份存储

### Linux依赖软件
```bash
# 安装rclone
curl https://rclone.org/install.sh | sudo bash

# 安装jq (用于JSON处理)
sudo apt-get install jq  # Ubuntu/Debian
sudo yum install jq      # CentOS/RHEL

# 可选：安装mailutils (用于邮件告警)
sudo apt-get install mailutils
```

### macOS依赖软件
```bash
# 安装Homebrew (如果未安装)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装rclone和jq
brew install rclone jq
```

## 快速开始

### 🐳 Docker部署 (推荐)

Docker方案支持跨平台部署，环境隔离，易于管理：

```bash
# 1. 克隆项目
git clone <repository> video-backup-system
cd video-backup-system

# 2. 配置环境变量
cp .env.template .env
# 编辑 .env 文件，设置MinIO配置

# 3. 一键部署
./docker-build.sh deploy

# 4. 查看状态
docker-compose ps
docker-compose logs -f video-backup
```

详细的Docker使用说明请参考：[DOCKER_README.md](DOCKER_README.md)

### 📦 传统部署

#### Linux系统

#### 1. 下载和配置

```bash
# 克隆或下载项目文件
git clone <repository> video-backup-system
cd video-backup-system

# 设置执行权限
chmod +x *.sh
```

#### 2. 配置rclone

系统已为你生成了rclone配置文件 `rclone.conf`，基于你的MinIO设置：

```ini
[minio-remote]
type = s3
provider = Minio
access_key_id = sndGbeN8OvYj3mifqX3o
secret_access_key = dbwsbEAGxnEif7DGZ2EhS88mxc5JG2Jn6HOygGMp
endpoint = http://144.126.146.223:9000
region = us-east-1
```

#### 3. 测试连接

```bash
# 测试rclone连接
rclone --config=./rclone.conf lsd minio-remote:records

# 如果成功，应该看到设备目录列表
```

### 4. 配置备份路径

编辑脚本中的备份路径（默认为 `/backup/video_archive`）：

```bash
# 在 video_backup_sync.sh 中修改
LOCAL_BACKUP_PATH="/your/backup/path"
```

### 5. 设置定时任务

```bash
# 自动设置cron定时任务
sudo ./setup_cron.sh
```

## 使用说明

### 手动执行备份

```bash
# 执行一次完整备份
./video_backup_sync.sh

# 查看备份日志
tail -f /var/log/video_backup.log
```

### 手动清理文件

```bash
# 清理本地过期文件（超过90天）
./video_cleanup.sh local

# 清理远程过期文件（超过7天）
./video_cleanup.sh remote

# 同时清理本地和远程
./video_cleanup.sh both
```

### 系统监控

```bash
# 检查系统状态
./monitor_system.sh check

# 生成详细报告
./monitor_system.sh report
```

## 定时任务说明

系统自动设置以下定时任务：

| 时间 | 任务 | 说明 |
|------|------|------|
| 每天 01:00 | 远程清理 | 确保远程服务器只保留7天内文件 |
| 每天 02:00 | 备份同步 | 备份超过7天的文件到本地并删除远程文件 |
| 每周日 03:00 | 本地清理 | 清理本地超过90天的备份文件 |

## 配置参数

### 主要参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `DAYS_TO_KEEP` | 7 | 服务器保留天数 |
| `LOCAL_RETENTION_DAYS` | 90 | 本地备份保留天数 |
| `LOCAL_BACKUP_PATH` | `/backup/video_archive` | 本地备份路径 |
| `DISK_USAGE_THRESHOLD` | 85% | 磁盘使用率告警阈值 |

### 修改配置

编辑对应脚本文件中的配置部分：

```bash
# video_backup_sync.sh
DAYS_TO_KEEP=7
LOCAL_BACKUP_PATH="/backup/video_archive"

# video_cleanup.sh  
LOCAL_RETENTION_DAYS=90
REMOTE_RETENTION_DAYS=7
```

## 日志文件

| 日志文件 | 说明 |
|----------|------|
| `/var/log/video_backup.log` | 备份操作日志 |
| `/var/log/video_cleanup.log` | 清理操作日志 |
| `/var/log/video_monitor.log` | 系统监控日志 |
| `/var/log/video_backup_cron.log` | Cron任务日志 |

## 故障排除

### 常见问题

1. **rclone连接失败**
   ```bash
   # 检查网络连接
   ping 144.126.146.223
   
   # 测试MinIO连接
   rclone --config=./rclone.conf lsd minio-remote:
   ```

2. **磁盘空间不足**
   ```bash
   # 检查磁盘使用情况
   df -h
   
   # 手动清理本地备份
   ./video_cleanup.sh local
   ```

3. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x *.sh
   
   # 确保日志目录可写
   sudo mkdir -p /var/log
   sudo chown $USER:$USER /var/log/video_*.log
   ```

### 查看系统状态

```bash
# 查看cron任务
crontab -l

# 查看最近的备份日志
tail -50 /var/log/video_backup.log

# 检查系统健康状态
./monitor_system.sh check
```

## 安全建议

1. **定期检查日志**: 监控备份和清理操作的执行情况
2. **测试恢复**: 定期测试从备份中恢复文件
3. **监控磁盘空间**: 确保备份磁盘有足够空间
4. **网络稳定性**: 确保到MinIO服务器的网络连接稳定
5. **权限管理**: 限制配置文件的访问权限

## 技术支持

如有问题，请检查：
1. 日志文件中的错误信息
2. rclone配置是否正确
3. 网络连接是否正常
4. 磁盘空间是否充足

---

**注意**: 首次运行前请务必测试备份和恢复流程，确保数据安全！
