# 视频备份系统 (Video Backup System)

一个专为MinIO视频存储设计的自动化备份和生命周期管理系统。

## 功能特性

- ✅ **rclone清理管理**: 使用rclone定时清理MinIO服务器上7天前的视频文件
- ✅ **智能备份策略**: 自动将即将过期的文件备份到本地存储
- ✅ **增量同步**: 使用rclone进行高效的增量备份
- ✅ **多设备支持**: 自动处理多个设备目录
- ✅ **监控告警**: 实时监控备份状态，支持邮件告警
- ✅ **日志管理**: 完整的日志记录和自动清理
- ✅ **系统服务**: systemd服务和定时任务自动化
- ✅ **纯rclone方案**: 无需MinIO客户端，只使用rclone工具

## 系统架构

```
MinIO服务器 (保留7天)
    ↓ rclone每日同步
本地备份服务器 (保留30天)
    ↓ 可选：云端备份
长期存储 (365天+)
```

## 快速开始

### 1. 系统要求

- Linux系统 (Ubuntu/CentOS/RHEL)
- Root权限
- 网络连接到MinIO服务器
- 足够的本地存储空间

### 2. 安装系统

```bash
# 下载安装脚本
sudo chmod +x install.sh

# 运行安装
sudo ./install.sh
```

### 3. 配置系统

```bash
# 配置rclone连接
cd /opt/video-backup
sudo -u $USER ./setup-rclone.sh
```

### 4. 配置rclone清理策略

```bash
# 配置rclone清理策略
cd /opt/video-backup
sudo -u $USER ./setup-minio-lifecycle.sh

# 查看清理策略信息
./setup-minio-lifecycle.sh --show-policy

# 测试清理效果
./setup-minio-lifecycle.sh --test-cleanup

# 手动运行清理（可选）
./setup-minio-lifecycle.sh --run-cleanup
```

### 5. 启动服务

```bash
# 启动备份服务
video-backup start

# 查看状态
video-backup status
```

## 使用指南

### 管理命令

系统安装后，可以使用 `video-backup` 命令进行管理：

```bash
video-backup status      # 查看系统状态
video-backup start       # 启动备份服务
video-backup stop        # 停止备份服务
video-backup backup      # 立即执行备份
video-backup monitor     # 查看监控状态
video-backup logs        # 查看日志
video-backup config      # 重新配置
video-backup test        # 测试功能
video-backup help        # 显示帮助
```

### 手动操作

```bash
cd /opt/video-backup

# 测试备份功能
./video-backup.sh --test

# 备份指定天数前的文件
./video-backup.sh --days 2

# 干运行模式（查看将要备份的文件）
./video-backup.sh --dry-run

# 只执行清理操作
./video-backup.sh --cleanup

# 查看监控状态
./backup-monitor.sh --status

# 生成备份报告
./backup-monitor.sh --report

# 测试告警功能
./backup-monitor.sh --test-alert
```

## 配置说明

### 主配置文件

配置文件位于 `/opt/video-backup/backup-config.env`：

```bash
# MinIO配置
MINIO_URL="http://144.126.146.223:9000"
MINIO_ACCESS_KEY="your-access-key"
MINIO_SECRET_KEY="your-secret-key"

# 备份配置
BACKUP_PATH="/backup/videos"
MINIO_SOURCE="minio-source:records"

# 保留策略
MINIO_RETENTION_DAYS=7      # MinIO保留天数
LOCAL_RETENTION_DAYS=30     # 本地备份保留天数

# 邮件告警配置
ALERT_EMAIL="<EMAIL>"
SMTP_SERVER="smtp.example.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="password"
```

### 定时任务

系统会自动设置以下定时任务：

- **每天凌晨2点**: 执行备份任务
- **每天凌晨3点**: 执行rclone清理任务（在备份之后）
- **每30分钟**: 执行监控检查
- **每天早上8点**: 生成并发送日报
- **每周日凌晨4点**: 清理旧日志

## 监控和告警

### 状态监控

系统会监控以下指标：

- 备份任务执行状态
- 磁盘空间使用情况
- MinIO连接状态
- 错误和警告数量

### 告警机制

- **错误告警**: 备份失败、连接异常、磁盘空间不足
- **警告告警**: 磁盘使用率过高、发现错误日志
- **冷却机制**: 相同类型告警4小时内只发送一次
- **日报功能**: 每日备份状态汇总报告

## 故障排除

### 常见问题

1. **备份失败**
   ```bash
   # 检查rclone配置
   rclone config show minio-source
   
   # 测试连接
   rclone lsd minio-source:records
   
   # 查看详细日志
   video-backup logs
   ```

2. **磁盘空间不足**
   ```bash
   # 检查磁盘使用情况
   df -h /backup/videos
   
   # 手动清理旧备份
   video-backup cleanup
   ```

3. **邮件告警不工作**
   ```bash
   # 测试邮件功能
   echo "test" | mail -s "test" <EMAIL>
   
   # 测试系统告警
   ./backup-monitor.sh --test-alert
   ```

### 日志文件

- 主日志: `/var/log/video-backup/backup.log`
- 监控日志: `/var/log/video-backup/monitor.log`
- rclone日志: `/var/log/video-backup/rclone-*.log`
- 系统日志: `journalctl -u video-backup.service`

## 高级配置

### 云端备份

可以配置额外的云端备份：

```bash
# 配置云存储
rclone config create cloud-backup s3 \
    provider=AWS \
    access_key_id=your-key \
    secret_access_key=your-secret

# 添加云端同步任务
echo "0 4 * * 0 rclone sync /backup/videos cloud-backup:video-archive" | crontab -
```

### 自定义保留策略

修改 `/opt/video-backup/backup-config.env` 中的保留天数：

```bash
MINIO_RETENTION_DAYS=7      # MinIO保留天数
LOCAL_RETENTION_DAYS=30     # 本地保留天数
CLOUD_RETENTION_DAYS=365    # 云端保留天数
```

### 性能优化

对于大量文件的场景，可以调整rclone参数：

```bash
# 在video-backup.sh中添加更多rclone选项
--transfers=10              # 并发传输数
--checkers=20              # 并发检查数
--buffer-size=256M         # 缓冲区大小
--use-mmap                 # 使用内存映射
```

## 安全建议

1. **权限控制**: 确保备份目录权限正确设置
2. **网络安全**: 使用VPN或专用网络连接MinIO
3. **密钥管理**: 定期轮换访问密钥
4. **备份验证**: 定期验证备份文件完整性
5. **监控日志**: 定期检查系统日志

## 支持和维护

### 系统更新

```bash
# 更新脚本文件
cd /path/to/source
sudo ./install.sh

# 重启服务
video-backup restart
```

### 备份验证

```bash
# 验证备份完整性
rclone check minio-source:records /backup/videos --one-way

# 生成校验和
find /backup/videos -type f -exec md5sum {} \; > backup-checksums.txt
```

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交问题报告和功能请求。
