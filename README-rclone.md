# 视频备份系统 - 纯rclone方案

专为MinIO视频存储设计的纯rclone自动化备份和清理系统。

## 🚀 方案特点

- ✅ **纯rclone实现**: 无需MinIO客户端，只使用rclone工具
- ✅ **自动清理**: rclone定时清理MinIO服务器上7天前的视频文件
- ✅ **智能备份**: 自动将即将过期的文件备份到本地存储
- ✅ **增量同步**: 高效的增量备份，节省带宽
- ✅ **多设备支持**: 自动处理多个设备目录
- ✅ **完全自动化**: 无需人工干预，长期稳定运行
- ✅ **监控告警**: 实时监控，邮件告警
- ✅ **一键部署**: 快速安装配置

## 📋 系统要求

- Linux系统 (Ubuntu/CentOS/RHEL)
- Root权限
- 网络连接到MinIO服务器
- 足够的本地存储空间

## ⚡ 快速开始

### 方法一：一键安装（推荐）

```bash
# 确保有config.json文件
# 以root权限运行快速安装脚本
sudo ./quick-start.sh
```

### 方法二：分步安装

```bash
# 1. 安装系统
sudo ./install.sh

# 2. 配置rclone
cd /opt/video-backup
sudo -u $USER ./setup-rclone.sh

# 3. 配置清理策略
sudo -u $USER ./setup-minio-lifecycle.sh

# 4. 启动服务
video-backup start
```

## 🔧 管理命令

安装完成后，使用 `video-backup` 命令管理系统：

```bash
video-backup status      # 查看系统状态
video-backup start       # 启动备份服务
video-backup stop        # 停止备份服务
video-backup backup      # 立即执行备份
video-backup logs        # 查看日志
video-backup help        # 显示帮助
```

## ⏰ 自动化任务

系统会自动设置以下定时任务：

- **每天凌晨2点**: 执行备份任务
- **每天凌晨3点**: 执行rclone清理任务
- **每30分钟**: 执行监控检查
- **每天早上8点**: 生成并发送日报

## 📁 重要文件

- **安装目录**: `/opt/video-backup`
- **日志目录**: `/var/log/video-backup`
- **配置文件**: `/opt/video-backup/backup-config.env`
- **清理脚本**: `/opt/video-backup/rclone-cleanup.sh`

## 🛠️ 手动操作

```bash
cd /opt/video-backup

# 测试备份功能
./video-backup.sh --test

# 查看即将过期的文件
./setup-minio-lifecycle.sh --test-cleanup

# 手动运行清理
./setup-minio-lifecycle.sh --run-cleanup

# 查看监控状态
./backup-monitor.sh --status

# 生成备份报告
./backup-monitor.sh --report
```

## 📊 工作流程

```
1. 每天凌晨2点：备份即将过期的文件到本地
   MinIO (7天前的文件) → 本地存储

2. 每天凌晨3点：清理MinIO上的过期文件
   删除MinIO上7天前的文件

3. 每30分钟：监控检查
   检查备份状态、磁盘空间、连接状态

4. 每天早上8点：生成日报
   发送备份状态报告邮件
```

## ⚙️ 配置说明

主要配置在 `/opt/video-backup/backup-config.env`：

```bash
# MinIO配置
MINIO_URL="http://your-minio-server:9000"
MINIO_ACCESS_KEY="your-access-key"
MINIO_SECRET_KEY="your-secret-key"

# 备份配置
BACKUP_PATH="/backup/videos"
MINIO_RETENTION_DAYS=7      # MinIO保留天数
LOCAL_RETENTION_DAYS=30     # 本地保留天数

# 邮件告警（可选）
ALERT_EMAIL="<EMAIL>"
```

## 🔍 监控和告警

### 监控指标
- 备份任务执行状态
- 磁盘空间使用情况
- MinIO连接状态
- 错误和警告数量

### 告警类型
- **错误告警**: 备份失败、连接异常、磁盘空间不足
- **警告告警**: 磁盘使用率过高、发现错误日志
- **日报功能**: 每日备份状态汇总报告

## 🚨 故障排除

### 常见问题

1. **rclone连接失败**
   ```bash
   # 测试连接
   rclone lsd minio-source:records
   
   # 重新配置
   ./setup-rclone.sh
   ```

2. **备份失败**
   ```bash
   # 查看日志
   video-backup logs
   
   # 测试备份
   ./video-backup.sh --test
   ```

3. **清理不工作**
   ```bash
   # 测试清理
   ./setup-minio-lifecycle.sh --test-cleanup
   
   # 手动清理
   ./setup-minio-lifecycle.sh --run-cleanup
   ```

## 💡 优势对比

### vs MinIO原生生命周期策略
- ✅ 无需MinIO管理员权限
- ✅ 更灵活的清理逻辑
- ✅ 可自定义清理规则
- ✅ 完整的日志记录

### vs 其他备份方案
- ✅ 专为视频监控设计
- ✅ 处理大量小文件优化
- ✅ 支持多设备目录结构
- ✅ 完整的监控告警系统

## 📈 性能特点

- **增量同步**: 只传输变化的文件
- **并发处理**: 支持多线程传输
- **断点续传**: 网络中断后自动恢复
- **压缩传输**: 可选的传输压缩
- **内存优化**: 适合长期运行

## 🔒 安全特性

- **权限控制**: 最小权限原则
- **日志审计**: 完整的操作日志
- **错误处理**: 完善的异常处理机制
- **数据校验**: 可选的文件完整性检查

## 📞 支持

如有问题，请检查：
1. 日志文件：`/var/log/video-backup/`
2. 系统状态：`video-backup status`
3. 连接测试：`rclone lsd minio-source:records`

## 🎯 适用场景

- 视频监控系统存储管理
- 大量小文件的定期清理
- 需要灵活备份策略的场景
- 不想使用MinIO客户端的环境
- 需要完整监控告警的系统

---

**注意**: 本系统专为视频监控存储设计，在删除文件前会自动备份，确保数据安全。
