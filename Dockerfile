# 视频备份系统 Docker 镜像
FROM alpine:3.18

# 设置维护者信息
LABEL maintainer="Video Backup System"
LABEL description="Automated video backup system using rclone"

# 安装系统依赖
RUN apk add --no-cache \
    bash \
    curl \
    jq \
    tzdata \
    ca-certificates \
    coreutils \
    findutils \
    grep \
    sed \
    unzip \
    && rm -rf /var/cache/apk/*

# 安装rclone
RUN curl -O https://downloads.rclone.org/rclone-current-linux-amd64.zip \
    && unzip rclone-current-linux-amd64.zip \
    && cd rclone-*-linux-amd64 \
    && cp rclone /usr/bin/ \
    && chown root:root /usr/bin/rclone \
    && chmod 755 /usr/bin/rclone \
    && cd .. \
    && rm -rf rclone-*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/logs /app/backup /app/config

# 复制脚本文件
COPY video_backup_sync.sh /app/
COPY video_cleanup.sh /app/
COPY monitor_system.sh /app/
COPY docker-entrypoint.sh /app/

# 设置脚本执行权限
RUN chmod +x /app/*.sh

# 创建非root用户
RUN addgroup -g 1000 videobackup && \
    adduser -D -s /bin/bash -u 1000 -G videobackup videobackup

# 设置目录权限
RUN chown -R videobackup:videobackup /app

# 切换到非root用户
USER videobackup

# 设置环境变量
ENV BACKUP_PATH=/app/backup
ENV LOG_PATH=/app/logs
ENV CONFIG_PATH=/app/config

# 暴露健康检查端点（可选）
HEALTHCHECK --interval=30m --timeout=10s --start-period=5s --retries=3 \
    CMD /app/monitor_system.sh check || exit 1

# 设置入口点
ENTRYPOINT ["/app/docker-entrypoint.sh"]

# 默认命令
CMD ["daemon"]
