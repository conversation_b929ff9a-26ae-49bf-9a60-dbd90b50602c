version: '3.8'

services:
  video-backup:
    build: .
    container_name: video-backup-system
    restart: unless-stopped
    
    # 环境变量配置
    environment:
      # MinIO连接配置 (必需)
      - MINIO_URL=http://144.126.146.223:9000
      - MINIO_ACCESS_KEY=sndGbeN8OvYj3mifqX3o
      - MINIO_SECRET_KEY=dbwsbEAGxnEif7DGZ2EhS88mxc5JG2Jn6HOygGMp
      
      # 路径配置
      - BACKUP_PATH=/app/backup
      - LOG_PATH=/app/logs
      - CONFIG_PATH=/app/config
      
      # 定时任务配置 (分钟)
      - BACKUP_INTERVAL=1440    # 24小时执行一次备份
      - CLEANUP_INTERVAL=1440   # 24小时执行一次清理
      - MONITOR_INTERVAL=30     # 30分钟执行一次监控
      
      # 备份策略配置
      - DAYS_TO_KEEP=7          # 服务器保留7天
      - LOCAL_RETENTION_DAYS=90 # 本地保留90天
      
      # 时区设置
      - TZ=Asia/Shanghai
    
    # 数据卷挂载
    volumes:
      # 备份数据存储 (重要：持久化备份数据)
      - ./backup:/app/backup
      
      # 日志存储 (可选：持久化日志)
      - ./logs:/app/logs
      
      # 配置文件存储 (可选：持久化配置)
      - ./config:/app/config
      
      # 如果需要自定义rclone配置
      # - ./rclone.conf:/home/<USER>/.config/rclone/rclone.conf:ro
    
    # 网络配置
    networks:
      - video-backup-net
    
    # 健康检查
    healthcheck:
      test: ["CMD", "/app/monitor_system.sh", "check"]
      interval: 30m
      timeout: 10s
      retries: 3
      start_period: 1m
    
    # 资源限制 (可选)
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  video-backup-net:
    driver: bridge

# 可选：如果需要使用外部MinIO服务
# networks:
#   video-backup-net:
#     external: true
