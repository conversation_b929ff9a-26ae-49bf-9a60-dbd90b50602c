#!/bin/bash

# Cron定时任务设置脚本
# 功能：自动配置视频备份和清理的定时任务
# 作者：自动生成

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_SCRIPT="$SCRIPT_DIR/video_backup_sync.sh"
CLEANUP_SCRIPT="$SCRIPT_DIR/video_cleanup.sh"
CRON_FILE="/tmp/video_backup_cron"

echo "设置视频备份系统的定时任务..."

# 检查脚本文件是否存在
if [ ! -f "$BACKUP_SCRIPT" ]; then
    echo "错误: 备份脚本不存在: $BACKUP_SCRIPT"
    exit 1
fi

if [ ! -f "$CLEANUP_SCRIPT" ]; then
    echo "错误: 清理脚本不存在: $CLEANUP_SCRIPT"
    exit 1
fi

# 设置脚本执行权限
chmod +x "$BACKUP_SCRIPT"
chmod +x "$CLEANUP_SCRIPT"

# 创建cron任务配置
cat > "$CRON_FILE" << EOF
# 视频备份系统定时任务
# 每天凌晨2点执行备份同步（将超过7天的视频备份到本地并从服务器删除）
0 2 * * * $BACKUP_SCRIPT >> /var/log/video_backup_cron.log 2>&1

# 每周日凌晨3点执行本地清理（清理本地超过90天的备份文件）
0 3 * * 0 $CLEANUP_SCRIPT local >> /var/log/video_cleanup_cron.log 2>&1

# 每天凌晨1点执行远程清理检查（确保远程只保留7天内的文件）
0 1 * * * $CLEANUP_SCRIPT remote >> /var/log/video_cleanup_cron.log 2>&1
EOF

# 安装cron任务
crontab "$CRON_FILE"

echo "定时任务已设置完成！"
echo ""
echo "当前的定时任务安排："
echo "- 每天凌晨1点：清理远程服务器上超过7天的文件"
echo "- 每天凌晨2点：备份超过7天的文件到本地并从服务器删除"
echo "- 每周日凌晨3点：清理本地超过90天的备份文件"
echo ""
echo "日志文件位置："
echo "- 备份日志: /var/log/video_backup.log"
echo "- 清理日志: /var/log/video_cleanup.log"
echo "- Cron日志: /var/log/video_backup_cron.log, /var/log/video_cleanup_cron.log"
echo ""
echo "查看当前cron任务: crontab -l"
echo "编辑cron任务: crontab -e"

# 清理临时文件
rm "$CRON_FILE"
