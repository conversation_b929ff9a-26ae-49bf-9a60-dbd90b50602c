#!/bin/bash

# Video Backup System - Quick Start Script (rclone-only)
# 视频备份系统 - 快速开始脚本（纯rclone方案）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo "========================================"
    echo "  视频备份系统 - 纯rclone方案"
    echo "========================================"
    echo
    log_info "本脚本将帮助你快速部署基于rclone的视频备份系统"
    echo
    echo "功能特点："
    echo "✅ 只使用rclone，无需MinIO客户端"
    echo "✅ 自动备份即将过期的视频文件"
    echo "✅ 定时清理MinIO服务器上的旧文件"
    echo "✅ 完整的监控和告警系统"
    echo "✅ 一键安装和配置"
    echo
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查是否为root用户
    if [[ $EUID -ne 0 ]]; then
        log_error "请以root权限运行此脚本: sudo $0"
        exit 1
    fi
    
    # 检查必要文件
    local required_files=("install.sh" "setup-rclone.sh" "config.json")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    log_info "系统要求检查通过"
}

# 执行安装
run_installation() {
    log_info "开始安装视频备份系统..."
    
    if ./install.sh; then
        log_info "系统安装成功"
    else
        log_error "系统安装失败"
        exit 1
    fi
}

# 配置rclone
configure_rclone() {
    log_info "配置rclone连接..."
    
    cd /opt/video-backup
    
    if sudo -u "${SUDO_USER:-$USER}" ./setup-rclone.sh; then
        log_info "rclone配置成功"
    else
        log_error "rclone配置失败"
        exit 1
    fi
}

# 配置清理策略
setup_cleanup_policy() {
    log_info "配置rclone清理策略..."
    
    cd /opt/video-backup
    
    if sudo -u "${SUDO_USER:-$USER}" ./setup-minio-lifecycle.sh --force; then
        log_info "清理策略配置成功"
    else
        log_error "清理策略配置失败"
        exit 1
    fi
}

# 启动服务
start_services() {
    log_info "启动备份服务..."
    
    if /usr/local/bin/video-backup start; then
        log_info "备份服务启动成功"
    else
        log_error "备份服务启动失败"
        exit 1
    fi
}

# 运行测试
run_tests() {
    log_info "运行系统测试..."
    
    cd /opt/video-backup
    
    # 测试rclone连接
    log_info "测试rclone连接..."
    if sudo -u "${SUDO_USER:-$USER}" rclone lsd minio-source:records &>/dev/null; then
        log_info "✅ rclone连接测试通过"
    else
        log_warn "❌ rclone连接测试失败"
    fi
    
    # 测试备份脚本
    log_info "测试备份脚本..."
    if sudo -u "${SUDO_USER:-$USER}" ./video-backup.sh --test &>/dev/null; then
        log_info "✅ 备份脚本测试通过"
    else
        log_warn "❌ 备份脚本测试失败"
    fi
    
    # 测试监控脚本
    log_info "测试监控脚本..."
    if sudo -u "${SUDO_USER:-$USER}" ./backup-monitor.sh --check &>/dev/null; then
        log_info "✅ 监控脚本测试通过"
    else
        log_warn "❌ 监控脚本测试失败"
    fi
    
    # 测试清理策略
    log_info "测试清理策略..."
    if sudo -u "${SUDO_USER:-$USER}" ./setup-minio-lifecycle.sh --test-cleanup &>/dev/null; then
        log_info "✅ 清理策略测试通过"
    else
        log_warn "❌ 清理策略测试失败"
    fi
}

# 显示完成信息
show_completion() {
    echo
    echo "========================================"
    log_info "🎉 视频备份系统安装完成！"
    echo "========================================"
    echo
    echo "📋 系统状态："
    /usr/local/bin/video-backup status
    echo
    echo "🔧 管理命令："
    echo "  video-backup status    # 查看系统状态"
    echo "  video-backup backup    # 立即执行备份"
    echo "  video-backup logs      # 查看日志"
    echo "  video-backup help      # 显示帮助"
    echo
    echo "📁 重要目录："
    echo "  安装目录: /opt/video-backup"
    echo "  日志目录: /var/log/video-backup"
    echo "  配置文件: /opt/video-backup/backup-config.env"
    echo
    echo "⏰ 定时任务："
    echo "  每天凌晨2点: 自动备份"
    echo "  每天凌晨3点: 自动清理"
    echo "  每30分钟: 监控检查"
    echo "  每天早上8点: 生成日报"
    echo
    echo "📖 详细文档请查看: README.md"
    echo
    log_info "系统已准备就绪，将自动开始工作！"
}

# 主函数
main() {
    show_welcome
    
    # 确认继续
    read -p "是否继续安装? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "用户取消安装"
        exit 0
    fi
    
    check_requirements
    run_installation
    configure_rclone
    setup_cleanup_policy
    start_services
    run_tests
    show_completion
}

# 显示帮助
show_help() {
    cat << EOF
视频备份系统快速开始脚本

用法: sudo $0 [选项]

选项:
    -h, --help      显示帮助信息
    --no-test       跳过测试步骤
    --no-start      不自动启动服务

说明:
    本脚本将自动完成以下步骤:
    1. 安装系统依赖和rclone
    2. 配置rclone连接到MinIO
    3. 创建rclone清理脚本
    4. 设置定时任务和系统服务
    5. 启动备份服务
    6. 运行系统测试

要求:
    - 需要root权限运行
    - 需要config.json配置文件
    - 需要网络连接到MinIO服务器

EOF
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
