# 项目文件说明

## 核心文件列表

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `README.md` | 主要说明文档 | 详细的安装和使用指南 |
| `config.json` | MinIO配置文件 | 包含MinIO服务器连接信息 |
| `rclone.conf` | rclone配置文件 | 基于config.json生成的rclone配置 |
| `video_backup_sync.sh` | 核心备份脚本 | 主要的备份同步逻辑 |
| `video_cleanup.sh` | 清理脚本 | 清理本地和远程过期文件 |
| `monitor_system.sh` | 监控脚本 | 系统状态监控和告警 |
| `setup_cron.sh` | 定时任务设置 | 自动配置cron定时任务 |

## 快速使用

1. **设置权限**：
   ```bash
   chmod +x *.sh
   ```

2. **测试连接**：
   ```bash
   rclone --config=./rclone.conf lsd minio-remote:records
   ```

3. **配置定时任务**：
   ```bash
   sudo ./setup_cron.sh
   ```

4. **手动执行备份**：
   ```bash
   ./video_backup_sync.sh
   ```

5. **查看系统状态**：
   ```bash
   ./monitor_system.sh check
   ```

## 工作流程

1. **每天凌晨1点**：清理远程服务器上超过7天的文件
2. **每天凌晨2点**：备份超过7天的文件到本地并从服务器删除
3. **每周日凌晨3点**：清理本地超过90天的备份文件

## 日志位置

- 备份日志：`/var/log/video_backup.log`
- 清理日志：`/var/log/video_cleanup.log`
- 监控日志：`/var/log/video_monitor.log`

## 注意事项

- 首次运行前请测试备份和恢复流程
- 确保有足够的本地磁盘空间
- 定期检查日志文件
- 建议在测试环境中先验证功能
