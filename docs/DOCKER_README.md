# 视频备份系统 Docker 版本

基于Docker的MinIO视频文件自动备份和管理系统，支持跨平台部署，环境隔离，易于管理。

## 🐳 Docker方案优势

- ✅ **跨平台支持**: Linux、macOS、Windows都可运行
- ✅ **环境隔离**: 不影响宿主机环境
- ✅ **易于部署**: 一键启动，无需复杂配置
- ✅ **版本管理**: 支持镜像版本控制
- ✅ **资源控制**: 可限制CPU和内存使用
- ✅ **数据持久化**: 备份数据和日志持久保存
- ✅ **健康检查**: 自动监控容器状态

## 📋 系统要求

- Docker Engine 20.10+
- Docker Compose 2.0+
- 足够的磁盘空间用于备份存储
- 网络连接到MinIO服务器

## 🚀 快速开始

### 方法一：使用 Docker Compose (推荐)

```bash
# 1. 克隆项目
git clone <repository> video-backup-docker
cd video-backup-docker

# 2. 配置环境变量
cp .env.template .env
# 编辑 .env 文件，设置你的MinIO配置

# 3. 启动服务
docker-compose up -d

# 4. 查看日志
docker-compose logs -f video-backup
```

### 方法二：直接使用 Docker

```bash
# 1. 构建镜像
docker build -t video-backup .

# 2. 运行容器
docker run -d \
  --name video-backup-system \
  --restart unless-stopped \
  -e MINIO_URL=http://144.126.146.223:9000 \
  -e MINIO_ACCESS_KEY=your_access_key \
  -e MINIO_SECRET_KEY=your_secret_key \
  -v $(pwd)/backup:/app/backup \
  -v $(pwd)/logs:/app/logs \
  video-backup
```

## ⚙️ 配置说明

### 环境变量

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `MINIO_URL` | ✅ | - | MinIO服务器地址 |
| `MINIO_ACCESS_KEY` | ✅ | - | MinIO访问密钥 |
| `MINIO_SECRET_KEY` | ✅ | - | MinIO密钥 |
| `BACKUP_INTERVAL` | ❌ | 1440 | 备份间隔(分钟) |
| `CLEANUP_INTERVAL` | ❌ | 1440 | 清理间隔(分钟) |
| `MONITOR_INTERVAL` | ❌ | 30 | 监控间隔(分钟) |
| `DAYS_TO_KEEP` | ❌ | 7 | 服务器保留天数 |
| `LOCAL_RETENTION_DAYS` | ❌ | 90 | 本地保留天数 |

### 数据卷挂载

| 容器路径 | 说明 | 重要性 |
|----------|------|--------|
| `/app/backup` | 备份数据存储 | 🔴 必需持久化 |
| `/app/logs` | 日志文件存储 | 🟡 建议持久化 |
| `/app/config` | 配置文件存储 | 🟢 可选持久化 |

## 🔧 管理命令

### 基本操作

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f video-backup
```

### 执行单次任务

```bash
# 立即执行备份
docker-compose exec video-backup /app/docker-entrypoint.sh backup

# 执行清理任务
docker-compose exec video-backup /app/docker-entrypoint.sh cleanup

# 查看系统状态
docker-compose exec video-backup /app/docker-entrypoint.sh status

# 执行监控检查
docker-compose exec video-backup /app/docker-entrypoint.sh monitor
```

### 进入容器调试

```bash
# 进入容器shell
docker-compose exec video-backup /bin/bash

# 查看rclone配置
docker-compose exec video-backup rclone config show

# 测试MinIO连接
docker-compose exec video-backup rclone lsd minio-remote:records
```

## 📊 监控和日志

### 健康检查

```bash
# 查看容器健康状态
docker-compose ps

# 查看健康检查日志
docker inspect video-backup-system | jq '.[0].State.Health'
```

### 日志管理

```bash
# 查看实时日志
docker-compose logs -f video-backup

# 查看最近100行日志
docker-compose logs --tail=100 video-backup

# 查看特定时间的日志
docker-compose logs --since="2024-01-01T00:00:00" video-backup
```

### 备份数据查看

```bash
# 查看备份目录结构
ls -la ./backup/

# 查看备份统计
du -sh ./backup/*

# 查看日志文件
tail -f ./logs/video_backup.log
```

## 🔄 定时任务说明

容器内的定时任务：

| 任务 | 默认间隔 | 说明 |
|------|----------|------|
| 备份同步 | 24小时 | 备份超过7天的文件到本地 |
| 远程清理 | 24小时 | 清理服务器上已备份的文件 |
| 系统监控 | 30分钟 | 检查系统状态和连接 |

可通过环境变量调整间隔时间。

## 🛠️ 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细错误信息
   docker-compose logs video-backup
   
   # 检查环境变量配置
   docker-compose config
   ```

2. **MinIO连接失败**
   ```bash
   # 测试网络连接
   docker-compose exec video-backup ping 144.126.146.223
   
   # 测试rclone连接
   docker-compose exec video-backup rclone lsd minio-remote:
   ```

3. **磁盘空间不足**
   ```bash
   # 查看磁盘使用情况
   df -h
   
   # 清理旧的备份文件
   docker-compose exec video-backup /app/docker-entrypoint.sh cleanup-local
   ```

4. **权限问题**
   ```bash
   # 检查挂载目录权限
   ls -la ./backup ./logs
   
   # 修复权限
   sudo chown -R 1000:1000 ./backup ./logs
   ```

### 调试模式

```bash
# 以调试模式运行容器
docker run --rm -it \
  -e MINIO_URL=http://144.126.146.223:9000 \
  -e MINIO_ACCESS_KEY=your_key \
  -e MINIO_SECRET_KEY=your_secret \
  video-backup /bin/bash
```

## 🔒 安全建议

1. **环境变量安全**：
   - 使用 `.env` 文件管理敏感信息
   - 不要将密钥提交到版本控制系统

2. **网络安全**：
   - 使用Docker网络隔离
   - 限制容器的网络访问

3. **数据安全**：
   - 定期备份重要数据
   - 设置适当的文件权限

4. **资源限制**：
   - 设置内存和CPU限制
   - 监控资源使用情况

## 📈 性能优化

1. **存储优化**：
   ```yaml
   # 使用SSD存储提高性能
   volumes:
     - type: bind
       source: /fast-storage/backup
       target: /app/backup
   ```

2. **网络优化**：
   ```bash
   # 调整rclone传输参数
   -e RCLONE_TRANSFERS=8
   -e RCLONE_CHECKERS=16
   ```

3. **资源分配**：
   ```yaml
   deploy:
     resources:
       limits:
         memory: 1G
         cpus: '1.0'
   ```

## 🔄 更新和维护

```bash
# 更新镜像
docker-compose pull
docker-compose up -d

# 备份配置
cp .env .env.backup
cp docker-compose.yml docker-compose.yml.backup

# 清理旧镜像
docker image prune -f
```

---

**注意**: 首次运行前请确保MinIO配置正确，并测试网络连接！
