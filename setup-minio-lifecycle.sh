#!/bin/bash

# Video Backup System - rclone-based Lifecycle Management Script
# 视频备份系统 - 基于rclone的生命周期管理脚本

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/config.json"
BACKUP_CONFIG="$SCRIPT_DIR/backup-config.env"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要文件
check_files() {
    log_info "检查必要文件..."

    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "找不到MinIO配置文件: $CONFIG_FILE"
        exit 1
    fi

    log_info "文件检查通过"
}

# 检查rclone安装
check_rclone() {
    log_info "检查rclone安装状态..."

    if command -v rclone &> /dev/null; then
        log_info "rclone已安装，版本: $(rclone version | head -n1)"
        return 0
    else
        log_error "rclone未安装，请先运行 setup-rclone.sh"
        exit 1
    fi
}

# 读取MinIO配置
read_minio_config() {
    log_info "读取MinIO配置..."
    
    # 使用jq解析JSON，如果没有jq则手动解析
    if command -v jq &> /dev/null; then
        MINIO_URL=$(jq -r '.aliases.minio.url' "$CONFIG_FILE")
        MINIO_ACCESS_KEY=$(jq -r '.aliases.minio.accessKey' "$CONFIG_FILE")
        MINIO_SECRET_KEY=$(jq -r '.aliases.minio.secretKey' "$CONFIG_FILE")
    else
        log_warn "未安装jq，使用简单解析方法"
        MINIO_URL=$(grep -o '"url": *"[^"]*"' "$CONFIG_FILE" | cut -d'"' -f4)
        MINIO_ACCESS_KEY=$(grep -o '"accessKey": *"[^"]*"' "$CONFIG_FILE" | cut -d'"' -f4)
        MINIO_SECRET_KEY=$(grep -o '"secretKey": *"[^"]*"' "$CONFIG_FILE" | cut -d'"' -f4)
    fi
    
    if [[ -z "$MINIO_URL" || -z "$MINIO_ACCESS_KEY" || -z "$MINIO_SECRET_KEY" ]]; then
        log_error "无法解析MinIO配置信息"
        exit 1
    fi
    
    log_info "MinIO配置读取成功"
    log_info "URL: $MINIO_URL"
    log_info "Access Key: ${MINIO_ACCESS_KEY:0:8}..."
}

# 测试rclone连接
test_rclone_connection() {
    log_info "测试rclone连接..."

    # 检查rclone配置是否存在
    if ! rclone config show minio-source &> /dev/null; then
        log_error "rclone MinIO配置不存在，请先运行 setup-rclone.sh"
        exit 1
    fi

    # 测试连接
    if rclone lsd minio-source: &> /dev/null; then
        log_info "rclone连接测试成功"
    else
        log_error "rclone连接测试失败"
        exit 1
    fi
}

# 检查records目录
check_records_directory() {
    log_info "检查records目录..."

    if rclone lsd minio-source:records &> /dev/null; then
        log_info "records目录存在"

        # 显示目录内容概览
        local device_count=$(rclone lsd minio-source:records | grep -c "device" || echo 0)
        log_info "发现 $device_count 个设备目录"

        # 显示前几个设备目录
        if [[ $device_count -gt 0 ]]; then
            log_info "设备目录示例:"
            rclone lsd minio-source:records | grep "device" | head -3 | while read -r line; do
                local device_name=$(echo "$line" | awk '{print $5}')
                log_info "  - $device_name"
            done
        fi
    else
        log_error "records目录不存在"
        log_info "请确认目录路径是否正确"
        exit 1
    fi
}

# 创建rclone清理脚本
create_cleanup_script() {
    log_info "创建rclone清理脚本..."

    local cleanup_script="$SCRIPT_DIR/rclone-cleanup.sh"

    cat > "$cleanup_script" << 'EOF'
#!/bin/bash

# rclone-based MinIO Cleanup Script
# 基于rclone的MinIO清理脚本

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/backup-config.env"

# 加载配置
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
else
    echo "错误: 找不到配置文件 $CONFIG_FILE"
    exit 1
fi

# 创建日志目录
mkdir -p "$LOG_DIR"

# 日志函数
log_cleanup() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_DIR/cleanup.log"
}

# 清理过期文件
cleanup_expired_files() {
    local retention_days="${MINIO_RETENTION_DAYS:-7}"
    local cutoff_date=$(date -d "${retention_days} days ago" '+%Y-%m-%d')

    log_cleanup "开始清理 $cutoff_date 之前的文件..."

    # 获取所有设备目录
    local devices=$(rclone lsd "$MINIO_SOURCE" --max-depth 1 2>/dev/null | awk '{print $5}' | grep '^device' || true)

    if [[ -z "$devices" ]]; then
        log_cleanup "未找到任何设备目录"
        return 0
    fi

    local total_deleted=0
    local total_size_deleted=0

    for device in $devices; do
        log_cleanup "处理设备: $device"

        # 获取设备目录下的所有日期目录
        local date_dirs=$(rclone lsd "$MINIO_SOURCE/$device" --max-depth 1 2>/dev/null | awk '{print $5}' || true)

        for date_dir in $date_dirs; do
            # 提取日期部分 (格式: 2025-01-23_20-46-05_hls)
            local dir_date=$(echo "$date_dir" | cut -d'_' -f1)

            # 检查日期是否早于截止日期
            if [[ "$dir_date" < "$cutoff_date" ]]; then
                log_cleanup "删除过期目录: $device/$date_dir (日期: $dir_date)"

                # 计算目录大小
                local dir_size=$(rclone size "$MINIO_SOURCE/$device/$date_dir" --json 2>/dev/null | jq -r '.bytes // 0' || echo 0)

                # 删除目录
                if rclone purge "$MINIO_SOURCE/$device/$date_dir" 2>/dev/null; then
                    ((total_deleted++))
                    total_size_deleted=$((total_size_deleted + dir_size))
                    log_cleanup "成功删除: $device/$date_dir"
                else
                    log_cleanup "删除失败: $device/$date_dir"
                fi
            fi
        done
    done

    # 转换字节为可读格式
    local size_mb=$((total_size_deleted / 1024 / 1024))
    log_cleanup "清理完成: 删除了 $total_deleted 个目录，释放空间 ${size_mb}MB"
}

# 主函数
main() {
    log_cleanup "========== rclone清理任务开始 =========="
    cleanup_expired_files
    log_cleanup "========== rclone清理任务完成 =========="
}

# 运行主函数
main "$@"
EOF

    chmod +x "$cleanup_script"
    log_info "清理脚本已创建: $cleanup_script"
}

# 显示清理策略信息
show_cleanup_policy() {
    log_info "rclone清理策略信息:"
    echo "================================"

    # 从配置文件读取保留天数
    local retention_days=7
    if [[ -f "$BACKUP_CONFIG" ]]; then
        source "$BACKUP_CONFIG"
        retention_days="${MINIO_RETENTION_DAYS:-7}"
    fi

    echo "清理方式: rclone定时清理"
    echo "应用路径: records/"
    echo "保留天数: $retention_days 天"
    echo "执行频率: 每天一次"
    echo "清理脚本: rclone-cleanup.sh"

    echo "================================"
    echo
    log_info "策略说明:"
    echo "- 使用rclone定时清理${retention_days}天前的视频文件"
    echo "- 只影响records/目录下的文件"
    echo "- 通过cron任务每天执行一次"
    echo "- 建议配合备份系统使用，避免数据丢失"
    echo "- 比MinIO原生生命周期更灵活，可自定义清理逻辑"
}

# 测试清理效果
test_cleanup() {
    log_info "测试清理效果..."

    # 从配置文件读取保留天数
    local retention_days=7
    if [[ -f "$BACKUP_CONFIG" ]]; then
        source "$BACKUP_CONFIG"
        retention_days="${MINIO_RETENTION_DAYS:-7}"
    fi

    # 查找即将过期的文件
    local test_date=$(date -d "$((retention_days - 1)) days ago" '+%Y-%m-%d')
    log_info "查找 $test_date 的文件（即将在1天后过期）..."

    local expiring_count=0
    local devices=$(rclone lsd minio-source:records --max-depth 1 2>/dev/null | awk '{print $5}' | grep '^device' || true)

    for device in $devices; do
        local date_dirs=$(rclone lsd "minio-source:records/$device" --max-depth 1 2>/dev/null | awk '{print $5}' | grep "$test_date" || true)
        for date_dir in $date_dirs; do
            log_info "  即将过期: $device/$date_dir"
            ((expiring_count++))
        done
    done

    if [[ $expiring_count -eq 0 ]]; then
        log_info "未找到即将过期的文件"
    else
        log_warn "发现 $expiring_count 个即将过期的目录，请确保备份系统正常运行"
    fi

    # 查找已过期的文件
    local expired_date=$(date -d "${retention_days} days ago" '+%Y-%m-%d')
    log_info "查找 $expired_date 之前的文件（应该被清理）..."

    local expired_count=0
    for device in $devices; do
        local date_dirs=$(rclone lsd "minio-source:records/$device" --max-depth 1 2>/dev/null | awk '{print $5}' || true)
        for date_dir in $date_dirs; do
            local dir_date=$(echo "$date_dir" | cut -d'_' -f1)
            if [[ "$dir_date" < "$expired_date" ]]; then
                log_warn "  发现过期文件: $device/$date_dir (日期: $dir_date)"
                ((expired_count++))
            fi
        done
    done

    if [[ $expired_count -eq 0 ]]; then
        log_info "未发现过期文件，清理策略工作正常"
    else
        log_warn "发现 $expired_count 个过期目录，建议运行清理脚本"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
rclone清理策略配置脚本

用法: $0 [选项]

选项:
    -h, --help          显示帮助信息
    -t, --test          只测试连接，不创建清理脚本
    -f, --force         强制创建清理脚本，不询问确认
    --show-policy       只显示清理策略信息
    --test-cleanup      测试即将过期和已过期的文件
    --run-cleanup       立即运行清理脚本

示例:
    $0                  # 交互式配置
    $0 --test          # 测试连接
    $0 --force         # 强制创建清理脚本
    $0 --show-policy   # 显示策略信息
    $0 --test-cleanup  # 测试清理效果
    $0 --run-cleanup   # 立即执行清理

EOF
}

# 运行清理脚本
run_cleanup() {
    local cleanup_script="$SCRIPT_DIR/rclone-cleanup.sh"

    if [[ ! -f "$cleanup_script" ]]; then
        log_error "清理脚本不存在，请先创建: $cleanup_script"
        exit 1
    fi

    log_info "运行清理脚本..."
    "$cleanup_script"
}

# 主函数
main() {
    local test_only=false
    local force_create=false
    local show_policy_only=false
    local test_cleanup_only=false
    local run_cleanup_only=false

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -t|--test)
                test_only=true
                shift
                ;;
            -f|--force)
                force_create=true
                shift
                ;;
            --show-policy)
                show_policy_only=true
                shift
                ;;
            --test-cleanup)
                test_cleanup_only=true
                shift
                ;;
            --run-cleanup)
                run_cleanup_only=true
                shift
                ;;
            *)
                echo "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    log_info "开始配置rclone清理策略..."

    # 只显示策略信息
    if [[ "$show_policy_only" == true ]]; then
        show_cleanup_policy
        exit 0
    fi

    check_files
    check_rclone
    read_minio_config
    test_rclone_connection
    check_records_directory

    # 只运行清理
    if [[ "$run_cleanup_only" == true ]]; then
        run_cleanup
        exit 0
    fi

    # 只测试清理效果
    if [[ "$test_cleanup_only" == true ]]; then
        test_cleanup
        exit 0
    fi

    # 只测试连接
    if [[ "$test_only" == true ]]; then
        log_info "连接测试完成"
        exit 0
    fi

    # 显示策略信息
    show_cleanup_policy

    # 确认创建清理脚本
    if [[ "$force_create" != true ]]; then
        echo
        read -p "是否要创建rclone清理脚本? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "用户取消操作"
            exit 0
        fi
    fi

    # 创建清理脚本
    create_cleanup_script

    # 测试清理效果
    test_cleanup

    log_info "rclone清理策略配置完成！"
    echo
    log_info "下一步建议:"
    echo "1. 将清理脚本添加到cron任务中:"
    echo "   echo '0 3 * * * $SCRIPT_DIR/rclone-cleanup.sh' | crontab -"
    echo "2. 配置并启动备份系统"
    echo "3. 监控备份和清理任务执行情况"
    echo "4. 定期检查即将过期的文件: $0 --test-cleanup"
}

# 运行主函数
main "$@"
