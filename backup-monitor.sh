#!/bin/bash

# Video Backup System - Monitoring and Alerting Script
# 视频备份系统 - 监控和告警脚本

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/backup-config.env"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 加载配置
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
else
    echo "错误: 找不到配置文件 $CONFIG_FILE"
    exit 1
fi

# 监控配置
MONITOR_LOG="$LOG_DIR/monitor.log"
STATUS_FILE="$LOG_DIR/backup-status.json"
ALERT_COOLDOWN_FILE="$LOG_DIR/alert-cooldown"
ALERT_COOLDOWN_HOURS=4

# 创建必要目录
mkdir -p "$LOG_DIR"

# 日志函数
log_monitor() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$MONITOR_LOG"
}

# 发送告警（带冷却时间）
send_alert_with_cooldown() {
    local alert_type="$1"
    local subject="$2"
    local message="$3"
    
    local cooldown_file="${ALERT_COOLDOWN_FILE}_${alert_type}"
    local current_time=$(date +%s)
    
    # 检查冷却时间
    if [[ -f "$cooldown_file" ]]; then
        local last_alert_time=$(cat "$cooldown_file" 2>/dev/null || echo 0)
        local time_diff=$((current_time - last_alert_time))
        local cooldown_seconds=$((ALERT_COOLDOWN_HOURS * 3600))
        
        if [[ $time_diff -lt $cooldown_seconds ]]; then
            log_monitor "告警被冷却抑制: $alert_type (剩余 $((cooldown_seconds - time_diff)) 秒)"
            return 0
        fi
    fi
    
    # 发送告警
    if [[ -n "$ALERT_EMAIL" ]]; then
        local full_message="时间: $(date)\n主机: $(hostname)\n\n$message"
        
        if command -v mail &>/dev/null; then
            echo -e "$full_message" | mail -s "$subject" "$ALERT_EMAIL" 2>/dev/null || true
        elif command -v sendmail &>/dev/null; then
            {
                echo "To: $ALERT_EMAIL"
                echo "Subject: $subject"
                echo ""
                echo -e "$full_message"
            } | sendmail "$ALERT_EMAIL" 2>/dev/null || true
        fi
        
        log_monitor "告警已发送: $subject"
        echo "$current_time" > "$cooldown_file"
    fi
}

# 检查备份任务状态
check_backup_status() {
    local status="OK"
    local issues=()
    
    # 检查最近的备份日志
    local latest_log=$(find "$LOG_DIR" -name "backup.log" -mtime -1 2>/dev/null | head -1)
    
    if [[ -z "$latest_log" ]]; then
        status="ERROR"
        issues+=("未找到最近24小时的备份日志")
    else
        # 检查备份是否成功
        if ! grep -q "备份任务成功完成" "$latest_log" 2>/dev/null; then
            status="ERROR"
            issues+=("最近的备份任务未成功完成")
        fi
        
        # 检查是否有错误
        local error_count=$(grep -c "\[ERROR\]" "$latest_log" 2>/dev/null || echo 0)
        if [[ $error_count -gt 0 ]]; then
            status="WARNING"
            issues+=("发现 $error_count 个错误")
        fi
    fi
    
    # 检查磁盘空间
    local backup_disk_usage=$(df "$LOCAL_BACKUP" | awk 'NR==2 {print int($5)}' | sed 's/%//')
    if [[ $backup_disk_usage -gt 90 ]]; then
        status="ERROR"
        issues+=("备份磁盘使用率过高: ${backup_disk_usage}%")
    elif [[ $backup_disk_usage -gt 80 ]]; then
        if [[ "$status" != "ERROR" ]]; then
            status="WARNING"
        fi
        issues+=("备份磁盘使用率较高: ${backup_disk_usage}%")
    fi
    
    # 检查MinIO连接
    if ! rclone lsd "$MINIO_SOURCE" &>/dev/null; then
        status="ERROR"
        issues+=("无法连接到MinIO服务器")
    fi
    
    # 更新状态文件
    cat > "$STATUS_FILE" << EOF
{
    "timestamp": "$(date -Iseconds)",
    "status": "$status",
    "backup_disk_usage": "${backup_disk_usage}%",
    "issues": [$(printf '"%s",' "${issues[@]}" | sed 's/,$//')],
    "last_check": "$(date '+%Y-%m-%d %H:%M:%S')"
}
EOF
    
    # 发送告警
    if [[ "$status" == "ERROR" ]]; then
        local issue_text=$(printf "%s\n" "${issues[@]}")
        send_alert_with_cooldown "backup_error" "视频备份系统错误" "发现以下问题:\n\n$issue_text"
    elif [[ "$status" == "WARNING" ]]; then
        local issue_text=$(printf "%s\n" "${issues[@]}")
        send_alert_with_cooldown "backup_warning" "视频备份系统警告" "发现以下警告:\n\n$issue_text"
    fi
    
    log_monitor "备份状态检查完成: $status"
    return 0
}

# 生成备份报告
generate_backup_report() {
    local report_file="$LOG_DIR/backup-report-$(date '+%Y%m%d').txt"
    
    cat > "$report_file" << EOF
视频备份系统日报
================

生成时间: $(date '+%Y-%m-%d %H:%M:%S')
主机名: $(hostname)

系统状态:
---------
EOF
    
    if [[ -f "$STATUS_FILE" ]]; then
        local status=$(jq -r '.status' "$STATUS_FILE" 2>/dev/null || echo "未知")
        local disk_usage=$(jq -r '.backup_disk_usage' "$STATUS_FILE" 2>/dev/null || echo "未知")
        
        echo "当前状态: $status" >> "$report_file"
        echo "磁盘使用率: $disk_usage" >> "$report_file"
        echo "" >> "$report_file"
    fi
    
    # 备份统计
    echo "备份统计:" >> "$report_file"
    echo "---------" >> "$report_file"
    
    if [[ -d "$LOCAL_BACKUP" ]]; then
        local total_size=$(du -sh "$LOCAL_BACKUP" 2>/dev/null | cut -f1 || echo "未知")
        local file_count=$(find "$LOCAL_BACKUP" -type f 2>/dev/null | wc -l || echo 0)
        local dir_count=$(find "$LOCAL_BACKUP" -maxdepth 1 -type d 2>/dev/null | wc -l || echo 0)
        
        echo "总备份大小: $total_size" >> "$report_file"
        echo "文件总数: $file_count" >> "$report_file"
        echo "备份天数: $((dir_count - 1))" >> "$report_file"
        echo "" >> "$report_file"
    fi
    
    # 最近的日志摘要
    echo "最近日志摘要:" >> "$report_file"
    echo "-------------" >> "$report_file"
    
    local latest_log=$(find "$LOG_DIR" -name "backup.log" -mtime -1 2>/dev/null | head -1)
    if [[ -n "$latest_log" ]]; then
        echo "最后备份时间: $(stat -c %y "$latest_log" 2>/dev/null | cut -d. -f1 || echo "未知")" >> "$report_file"
        
        local success_count=$(grep -c "备份任务成功完成" "$latest_log" 2>/dev/null || echo 0)
        local error_count=$(grep -c "\[ERROR\]" "$latest_log" 2>/dev/null || echo 0)
        local warning_count=$(grep -c "\[WARN\]" "$latest_log" 2>/dev/null || echo 0)
        
        echo "成功次数: $success_count" >> "$report_file"
        echo "错误次数: $error_count" >> "$report_file"
        echo "警告次数: $warning_count" >> "$report_file"
        
        if [[ $error_count -gt 0 ]]; then
            echo "" >> "$report_file"
            echo "最近错误:" >> "$report_file"
            grep "\[ERROR\]" "$latest_log" 2>/dev/null | tail -5 >> "$report_file" || true
        fi
    else
        echo "未找到最近的备份日志" >> "$report_file"
    fi
    
    log_monitor "备份报告已生成: $report_file"
    
    # 如果配置了邮件，发送日报
    if [[ -n "$ALERT_EMAIL" && $(date '+%H') -eq 8 ]]; then
        if command -v mail &>/dev/null; then
            mail -s "视频备份系统日报 - $(date '+%Y-%m-%d')" "$ALERT_EMAIL" < "$report_file" 2>/dev/null || true
        fi
    fi
}

# 清理监控日志
cleanup_monitor_logs() {
    log_monitor "清理监控日志..."
    
    # 删除30天前的监控日志
    find "$LOG_DIR" -name "monitor.log*" -mtime +30 -delete 2>/dev/null || true
    find "$LOG_DIR" -name "backup-report-*.txt" -mtime +30 -delete 2>/dev/null || true
    find "$LOG_DIR" -name "alert-cooldown_*" -mtime +7 -delete 2>/dev/null || true
    
    # 压缩7天前的日志
    find "$LOG_DIR" -name "*.log" -mtime +7 -not -name "*.gz" -exec gzip {} \; 2>/dev/null || true
}

# 显示当前状态
show_status() {
    echo "视频备份系统状态"
    echo "================"
    echo
    
    if [[ -f "$STATUS_FILE" ]]; then
        local status=$(jq -r '.status' "$STATUS_FILE" 2>/dev/null || echo "未知")
        local last_check=$(jq -r '.last_check' "$STATUS_FILE" 2>/dev/null || echo "未知")
        local disk_usage=$(jq -r '.backup_disk_usage' "$STATUS_FILE" 2>/dev/null || echo "未知")
        
        case $status in
            "OK")
                echo -e "状态: ${GREEN}正常${NC}"
                ;;
            "WARNING")
                echo -e "状态: ${YELLOW}警告${NC}"
                ;;
            "ERROR")
                echo -e "状态: ${RED}错误${NC}"
                ;;
            *)
                echo -e "状态: ${BLUE}未知${NC}"
                ;;
        esac
        
        echo "最后检查: $last_check"
        echo "磁盘使用率: $disk_usage"
        echo
        
        # 显示问题
        local issues=$(jq -r '.issues[]' "$STATUS_FILE" 2>/dev/null || true)
        if [[ -n "$issues" ]]; then
            echo "发现的问题:"
            echo "$issues" | sed 's/^/  - /'
            echo
        fi
    else
        echo "状态文件不存在，请先运行监控检查"
        echo
    fi
    
    # 显示备份统计
    if [[ -d "$LOCAL_BACKUP" ]]; then
        echo "备份统计:"
        echo "--------"
        local total_size=$(du -sh "$LOCAL_BACKUP" 2>/dev/null | cut -f1 || echo "未知")
        local file_count=$(find "$LOCAL_BACKUP" -type f 2>/dev/null | wc -l || echo 0)
        
        echo "总大小: $total_size"
        echo "文件数: $file_count"
        echo
    fi
}

# 显示帮助
show_help() {
    cat << EOF
视频备份系统监控脚本

用法: $0 [选项]

选项:
    -h, --help          显示帮助信息
    -s, --status        显示当前状态
    -c, --check         执行状态检查
    -r, --report        生成备份报告
    --cleanup           清理旧日志
    --test-alert        测试告警功能

示例:
    $0 --check          # 检查备份状态
    $0 --status         # 显示当前状态
    $0 --report         # 生成日报
    $0 --test-alert     # 测试邮件告警

EOF
}

# 测试告警
test_alert() {
    log_monitor "测试告警功能..."
    send_alert_with_cooldown "test" "视频备份系统测试" "这是一条测试告警消息\n\n系统运行正常"
    echo "测试告警已发送（如果配置了邮件）"
}

# 主函数
main() {
    case "${1:-check}" in
        -h|--help)
            show_help
            ;;
        -s|--status)
            show_status
            ;;
        -c|--check)
            log_monitor "开始监控检查..."
            check_backup_status
            ;;
        -r|--report)
            log_monitor "生成备份报告..."
            generate_backup_report
            ;;
        --cleanup)
            cleanup_monitor_logs
            ;;
        --test-alert)
            test_alert
            ;;
        *)
            # 默认执行检查
            check_backup_status
            ;;
    esac
}

# 运行主函数
main "$@"
