# Video Backup System Configuration Template
# 视频备份系统配置模板
# 
# 复制此文件为 backup-config.env 并修改相应配置
# cp backup-config.env.template backup-config.env

# ============================================================================
# MinIO 服务器配置
# ============================================================================

# MinIO服务器地址
MINIO_URL="http://144.126.146.223:9000"

# MinIO访问密钥
MINIO_ACCESS_KEY="sndGbeN8OvYj3mifqX3o"

# MinIO密钥
MINIO_SECRET_KEY="dbwsbEAGxnEif7DGZ2EhS88mxc5JG2Jn6HOygGMp"

# ============================================================================
# 备份路径配置
# ============================================================================

# 本地备份存储路径
BACKUP_PATH="/backup/videos"

# MinIO源路径 (通常不需要修改)
MINIO_SOURCE="minio-source:records"

# 本地备份路径 (与BACKUP_PATH相同)
LOCAL_BACKUP="/backup/videos"

# ============================================================================
# 日志配置
# ============================================================================

# 日志目录
LOG_DIR="/var/log/video-backup"

# 主日志文件
LOG_FILE="$LOG_DIR/backup.log"

# ============================================================================
# 数据保留策略
# ============================================================================

# MinIO服务器保留天数 (建议7天)
MINIO_RETENTION_DAYS=7

# 本地备份保留天数 (建议30天)
LOCAL_RETENTION_DAYS=30

# 云端备份保留天数 (可选，建议365天)
CLOUD_RETENTION_DAYS=365

# ============================================================================
# 备份时间配置
# ============================================================================

# 备份执行时间 - 小时 (0-23)
BACKUP_HOUR=2

# 备份执行时间 - 分钟 (0-59)
BACKUP_MINUTE=0

# 提前备份天数 (在文件过期前几天备份，建议1天)
BACKUP_DAYS_BEFORE_EXPIRY=1

# ============================================================================
# 邮件告警配置 (可选)
# ============================================================================

# 告警邮箱地址 (留空则不发送邮件告警)
ALERT_EMAIL=""

# SMTP服务器地址
SMTP_SERVER=""

# SMTP端口 (通常587或25)
SMTP_PORT=587

# SMTP用户名
SMTP_USER=""

# SMTP密码
SMTP_PASS=""

# 告警冷却时间 (小时，避免重复告警)
ALERT_COOLDOWN_HOURS=4

# ============================================================================
# rclone 配置
# ============================================================================

# rclone传输并发数
RCLONE_TRANSFERS=4

# rclone检查并发数
RCLONE_CHECKERS=8

# rclone缓冲区大小
RCLONE_BUFFER_SIZE="16M"

# rclone重试次数
RCLONE_RETRIES=3

# rclone重试间隔 (秒)
RCLONE_RETRY_DELAY=30

# rclone超时时间
RCLONE_TIMEOUT="1h"

# ============================================================================
# 监控配置
# ============================================================================

# 磁盘使用率警告阈值 (百分比)
DISK_WARNING_THRESHOLD=80

# 磁盘使用率错误阈值 (百分比)
DISK_ERROR_THRESHOLD=90

# 最小可用磁盘空间 (GB)
MIN_FREE_DISK_GB=10

# 监控检查间隔 (分钟)
MONITOR_INTERVAL=30

# ============================================================================
# 高级配置
# ============================================================================

# 是否启用详细日志
VERBOSE_LOGGING=true

# 是否启用进度显示
SHOW_PROGRESS=true

# 是否启用统计信息
SHOW_STATS=true

# 统计信息更新间隔
STATS_INTERVAL="10m"

# 是否启用压缩传输
ENABLE_COMPRESSION=false

# 是否启用校验和验证
ENABLE_CHECKSUM=true

# ============================================================================
# 云端备份配置 (可选)
# ============================================================================

# 是否启用云端备份
ENABLE_CLOUD_BACKUP=false

# 云端备份rclone配置名称
CLOUD_BACKUP_REMOTE=""

# 云端备份路径
CLOUD_BACKUP_PATH=""

# 云端备份频率 (天)
CLOUD_BACKUP_FREQUENCY=7

# ============================================================================
# 数据库配置 (可选，用于记录备份历史)
# ============================================================================

# 是否启用数据库记录
ENABLE_DATABASE=false

# 数据库类型 (sqlite/mysql/postgresql)
DATABASE_TYPE="sqlite"

# 数据库文件路径 (仅SQLite)
DATABASE_FILE="$LOG_DIR/backup.db"

# 数据库连接字符串 (MySQL/PostgreSQL)
DATABASE_URL=""

# ============================================================================
# 性能调优
# ============================================================================

# 最大并发备份任务数
MAX_CONCURRENT_BACKUPS=2

# 网络带宽限制 (KB/s，0表示无限制)
BANDWIDTH_LIMIT=0

# 内存使用限制 (MB)
MEMORY_LIMIT=512

# 临时文件目录
TEMP_DIR="/tmp/video-backup"

# ============================================================================
# 安全配置
# ============================================================================

# 是否启用SSL/TLS验证
VERIFY_SSL=true

# 客户端证书路径 (可选)
CLIENT_CERT=""

# 客户端密钥路径 (可选)
CLIENT_KEY=""

# CA证书路径 (可选)
CA_CERT=""

# ============================================================================
# 调试配置
# ============================================================================

# 调试模式 (true/false)
DEBUG_MODE=false

# 调试日志级别 (DEBUG/INFO/WARN/ERROR)
DEBUG_LEVEL="INFO"

# 是否保留临时文件
KEEP_TEMP_FILES=false

# ============================================================================
# 自定义钩子脚本 (可选)
# ============================================================================

# 备份前执行的脚本
PRE_BACKUP_HOOK=""

# 备份后执行的脚本
POST_BACKUP_HOOK=""

# 备份失败时执行的脚本
BACKUP_FAILURE_HOOK=""

# 清理前执行的脚本
PRE_CLEANUP_HOOK=""

# 清理后执行的脚本
POST_CLEANUP_HOOK=""
