#!/bin/bash

# Video Backup System - Main Backup Script
# 视频备份系统 - 主备份脚本

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/backup-config.env"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 加载配置
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
else
    echo "错误: 找不到配置文件 $CONFIG_FILE"
    echo "请先运行 setup-rclone.sh 进行配置"
    exit 1
fi

# 创建日志目录
mkdir -p "$LOG_DIR"

# 日志函数
log_with_timestamp() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_info() {
    log_with_timestamp "[INFO] $1"
}

log_warn() {
    log_with_timestamp "[WARN] $1"
}

log_error() {
    log_with_timestamp "[ERROR] $1"
}

# 发送告警邮件
send_alert() {
    local subject="$1"
    local message="$2"
    
    if [[ -n "$ALERT_EMAIL" && -n "$SMTP_SERVER" ]]; then
        # 尝试使用mail命令
        if command -v mail &>/dev/null; then
            echo "$message" | mail -s "$subject" "$ALERT_EMAIL" 2>/dev/null || true
        # 尝试使用sendmail
        elif command -v sendmail &>/dev/null; then
            {
                echo "To: $ALERT_EMAIL"
                echo "Subject: $subject"
                echo ""
                echo "$message"
            } | sendmail "$ALERT_EMAIL" 2>/dev/null || true
        fi
    fi
}

# 检查磁盘空间
check_disk_space() {
    local path="$1"
    local min_free_gb="$2"
    
    local available_gb=$(df "$path" | awk 'NR==2 {print int($4/1024/1024)}')
    
    if [[ $available_gb -lt $min_free_gb ]]; then
        log_error "磁盘空间不足: $path 只有 ${available_gb}GB 可用空间，需要至少 ${min_free_gb}GB"
        send_alert "备份失败 - 磁盘空间不足" "路径: $path\n可用空间: ${available_gb}GB\n需要空间: ${min_free_gb}GB"
        return 1
    fi
    
    log_info "磁盘空间检查通过: $path 有 ${available_gb}GB 可用空间"
    return 0
}

# 获取即将过期的文件列表
get_expiring_files() {
    local days_before_expiry="$1"
    local target_date=$(date -d "${days_before_expiry} days ago" '+%Y-%m-%d')
    
    log_info "查找 $target_date 的文件进行备份..."
    
    # 列出所有设备目录
    local devices=$(rclone lsd "$MINIO_SOURCE" --max-depth 1 2>/dev/null | awk '{print $5}' | grep '^device' || true)
    
    if [[ -z "$devices" ]]; then
        log_warn "未找到任何设备目录"
        return 1
    fi
    
    local file_count=0
    for device in $devices; do
        log_info "处理设备: $device"
        
        # 查找目标日期的目录
        local date_dirs=$(rclone lsd "$MINIO_SOURCE/$device" --max-depth 1 2>/dev/null | awk '{print $5}' | grep "$target_date" || true)
        
        for date_dir in $date_dirs; do
            log_info "找到目录: $device/$date_dir"
            ((file_count++))
        done
    done
    
    log_info "总共找到 $file_count 个目录需要备份"
    return 0
}

# 执行备份
perform_backup() {
    local days_before_expiry="${1:-1}"
    local target_date=$(date -d "${days_before_expiry} days ago" '+%Y-%m-%d')
    
    log_info "开始备份 $target_date 的视频文件..."
    
    # 检查磁盘空间（至少需要10GB）
    if ! check_disk_space "$LOCAL_BACKUP" 10; then
        return 1
    fi
    
    # 创建备份目录
    local backup_date_dir="$LOCAL_BACKUP/$target_date"
    mkdir -p "$backup_date_dir"
    
    # 执行rclone同步
    local rclone_log="$LOG_DIR/rclone-$(date '+%Y%m%d-%H%M%S').log"
    
    log_info "开始rclone同步，日志文件: $rclone_log"
    
    if rclone sync "$MINIO_SOURCE" "$backup_date_dir" \
        --include="*${target_date}*/**" \
        --log-file="$rclone_log" \
        --log-level=INFO \
        --retries=3 \
        --retries-sleep=30s \
        --timeout=1h \
        --stats=10m \
        --stats-one-line \
        --progress; then
        
        log_info "备份成功完成"
        
        # 统计备份信息
        local backup_size=$(du -sh "$backup_date_dir" 2>/dev/null | cut -f1 || echo "未知")
        local file_count=$(find "$backup_date_dir" -type f | wc -l)
        
        log_info "备份统计: 大小=$backup_size, 文件数=$file_count"
        
        return 0
    else
        log_error "备份失败，请检查rclone日志: $rclone_log"
        send_alert "视频备份失败" "日期: $target_date\n请检查日志文件: $rclone_log"
        return 1
    fi
}

# 清理本地旧备份
cleanup_local_backups() {
    log_info "清理本地旧备份文件..."
    
    local deleted_count=0
    
    # 删除超过保留期的备份
    if [[ -d "$LOCAL_BACKUP" ]]; then
        while IFS= read -r -d '' old_dir; do
            log_info "删除旧备份: $old_dir"
            rm -rf "$old_dir"
            ((deleted_count++))
        done < <(find "$LOCAL_BACKUP" -maxdepth 1 -type d -mtime +$LOCAL_RETENTION_DAYS -print0 2>/dev/null || true)
    fi
    
    log_info "清理完成，删除了 $deleted_count 个旧备份目录"
}

# 清理旧日志
cleanup_logs() {
    log_info "清理旧日志文件..."
    
    # 保留30天的日志
    find "$LOG_DIR" -name "*.log" -mtime +30 -delete 2>/dev/null || true
    
    # 压缩7天前的日志
    find "$LOG_DIR" -name "*.log" -mtime +7 -exec gzip {} \; 2>/dev/null || true
}

# 健康检查
health_check() {
    log_info "执行系统健康检查..."
    
    # 检查rclone配置
    if ! rclone config show minio-source &>/dev/null; then
        log_error "rclone MinIO配置不存在"
        return 1
    fi
    
    # 测试MinIO连接
    if ! rclone lsd "$MINIO_SOURCE" &>/dev/null; then
        log_error "无法连接到MinIO服务器"
        return 1
    fi
    
    # 检查备份目录
    if [[ ! -d "$LOCAL_BACKUP" ]]; then
        log_error "备份目录不存在: $LOCAL_BACKUP"
        return 1
    fi
    
    log_info "健康检查通过"
    return 0
}

# 显示使用帮助
show_help() {
    cat << EOF
视频备份系统 - 使用说明

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -t, --test          测试模式，不执行实际备份
    -d, --days N        备份N天前的文件 (默认: 1)
    -c, --cleanup       只执行清理操作
    --health-check      只执行健康检查
    --dry-run          干运行模式，显示将要备份的文件但不执行

示例:
    $0                  # 备份昨天的文件
    $0 -d 2            # 备份2天前的文件
    $0 --test          # 测试模式
    $0 --cleanup       # 只清理旧文件
    $0 --dry-run       # 查看将要备份的文件

EOF
}

# 主函数
main() {
    local days_before_expiry=1
    local test_mode=false
    local cleanup_only=false
    local health_check_only=false
    local dry_run=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -t|--test)
                test_mode=true
                shift
                ;;
            -d|--days)
                days_before_expiry="$2"
                shift 2
                ;;
            -c|--cleanup)
                cleanup_only=true
                shift
                ;;
            --health-check)
                health_check_only=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            *)
                echo "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "========== 视频备份系统启动 =========="
    log_info "配置文件: $CONFIG_FILE"
    log_info "日志文件: $LOG_FILE"
    
    # 执行健康检查
    if ! health_check; then
        log_error "健康检查失败，退出"
        exit 1
    fi
    
    if [[ "$health_check_only" == true ]]; then
        log_info "健康检查完成"
        exit 0
    fi
    
    # 只执行清理
    if [[ "$cleanup_only" == true ]]; then
        cleanup_local_backups
        cleanup_logs
        log_info "清理操作完成"
        exit 0
    fi
    
    # 干运行模式
    if [[ "$dry_run" == true ]]; then
        get_expiring_files "$days_before_expiry"
        log_info "干运行完成"
        exit 0
    fi
    
    # 测试模式
    if [[ "$test_mode" == true ]]; then
        log_info "测试模式：将备份 $days_before_expiry 天前的文件"
        get_expiring_files "$days_before_expiry"
        log_info "测试完成"
        exit 0
    fi
    
    # 执行备份
    if perform_backup "$days_before_expiry"; then
        log_info "备份任务成功完成"
        
        # 执行清理
        cleanup_local_backups
        cleanup_logs
        
        log_info "========== 备份系统任务完成 =========="
        exit 0
    else
        log_error "备份任务失败"
        log_info "========== 备份系统任务失败 =========="
        exit 1
    fi
}

# 运行主函数
main "$@"
