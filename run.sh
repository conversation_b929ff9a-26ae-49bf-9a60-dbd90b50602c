#!/bin/bash

# 视频备份系统快速启动脚本
# 功能：从项目根目录快速执行各种操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目目录
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_DIR="$PROJECT_DIR/scripts"
DOCKER_DIR="$PROJECT_DIR/docker"
SETUP_DIR="$PROJECT_DIR/setup"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
视频备份系统快速启动脚本

用法: $0 [命令] [选项]

📦 传统部署命令:
    backup              执行备份任务
    cleanup             执行清理任务 (远程)
    cleanup-local       执行本地清理任务
    monitor             执行监控检查
    status              显示系统状态
    setup-cron          设置Linux定时任务
    setup-macos         设置macOS环境

🐳 Docker部署命令:
    docker-build        构建Docker镜像
    docker-deploy       完整Docker部署
    docker-start        启动Docker服务
    docker-stop         停止Docker服务
    docker-logs         查看Docker日志
    docker-shell        进入Docker容器

🔧 工具命令:
    test-connection     测试rclone连接
    permissions         设置脚本权限
    structure           显示项目结构
    help                显示此帮助

示例:
    $0 backup                   # 执行备份
    $0 docker-deploy           # Docker部署
    $0 test-connection         # 测试连接
    $0 setup-cron              # 设置定时任务

EOF
}

# 设置脚本权限
set_permissions() {
    log_info "设置脚本执行权限..."
    chmod +x "$SCRIPTS_DIR"/*.sh
    chmod +x "$SETUP_DIR"/*.sh
    chmod +x "$DOCKER_DIR"/*.sh
    log_info "✅ 权限设置完成"
}

# 测试rclone连接
test_connection() {
    log_info "测试rclone连接..."
    if rclone --config="$PROJECT_DIR/config/rclone.conf" lsd minio-remote:records &> /dev/null; then
        log_info "✅ rclone连接测试成功"
    else
        log_error "❌ rclone连接测试失败"
        log_error "请检查 config/rclone.conf 配置文件"
        exit 1
    fi
}

# 显示项目结构
show_structure() {
    log_info "项目文件结构："
    tree -L 2 "$PROJECT_DIR" 2>/dev/null || find "$PROJECT_DIR" -maxdepth 2 -type d | sort
}

# 执行脚本命令
run_script() {
    local script_name=$1
    local script_path="$SCRIPTS_DIR/$script_name"
    
    if [[ ! -f "$script_path" ]]; then
        log_error "脚本不存在: $script_path"
        exit 1
    fi
    
    log_info "执行脚本: $script_name"
    cd "$SCRIPTS_DIR"
    "./$script_name" "${@:2}"
}

# 执行设置脚本
run_setup() {
    local setup_name=$1
    local setup_path="$SETUP_DIR/$setup_name"
    
    if [[ ! -f "$setup_path" ]]; then
        log_error "设置脚本不存在: $setup_path"
        exit 1
    fi
    
    log_info "执行设置脚本: $setup_name"
    cd "$SETUP_DIR"
    "./$setup_name" "${@:2}"
}

# 执行Docker命令
run_docker() {
    local docker_cmd=$1
    
    if [[ ! -d "$DOCKER_DIR" ]]; then
        log_error "Docker目录不存在: $DOCKER_DIR"
        exit 1
    fi
    
    cd "$DOCKER_DIR"
    
    case $docker_cmd in
        "build")
            ./docker-build.sh build "${@:2}"
            ;;
        "deploy")
            ./docker-build.sh deploy "${@:2}"
            ;;
        "start")
            docker-compose up -d
            ;;
        "stop")
            docker-compose down
            ;;
        "logs")
            docker-compose logs -f video-backup
            ;;
        "shell")
            docker-compose exec video-backup /bin/bash
            ;;
        *)
            log_error "未知的Docker命令: $docker_cmd"
            exit 1
            ;;
    esac
}

# 主函数
main() {
    local command=${1:-help}
    
    case $command in
        # 传统部署命令
        "backup")
            run_script "video_backup_sync.sh" "${@:2}"
            ;;
        "cleanup")
            run_script "video_cleanup.sh" "remote" "${@:2}"
            ;;
        "cleanup-local")
            run_script "video_cleanup.sh" "local" "${@:2}"
            ;;
        "monitor")
            run_script "monitor_system.sh" "check" "${@:2}"
            ;;
        "status")
            run_script "monitor_system.sh" "report" "${@:2}"
            ;;
        "setup-cron")
            run_setup "setup_cron.sh" "${@:2}"
            ;;
        "setup-macos")
            run_setup "setup_macos.sh" "${@:2}"
            ;;
        
        # Docker命令
        "docker-build")
            run_docker "build" "${@:2}"
            ;;
        "docker-deploy")
            run_docker "deploy" "${@:2}"
            ;;
        "docker-start")
            run_docker "start" "${@:2}"
            ;;
        "docker-stop")
            run_docker "stop" "${@:2}"
            ;;
        "docker-logs")
            run_docker "logs" "${@:2}"
            ;;
        "docker-shell")
            run_docker "shell" "${@:2}"
            ;;
        
        # 工具命令
        "test-connection")
            test_connection
            ;;
        "permissions")
            set_permissions
            ;;
        "structure")
            show_structure
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
