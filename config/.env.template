# 视频备份系统 Docker 环境变量配置模板
# 复制此文件为 .env 并修改相应配置
# cp .env.template .env

# ============================================================================
# MinIO 服务器配置 (必需)
# ============================================================================

# MinIO服务器地址
MINIO_URL=http://144.126.146.223:9000

# MinIO访问密钥
MINIO_ACCESS_KEY=sndGbeN8OvYj3mifqX3o

# MinIO密钥
MINIO_SECRET_KEY=dbwsbEAGxnEif7DGZ2EhS88mxc5JG2Jn6HOygGMp

# ============================================================================
# 路径配置
# ============================================================================

# 容器内备份路径 (通常不需要修改)
BACKUP_PATH=/app/backup

# 容器内日志路径 (通常不需要修改)
LOG_PATH=/app/logs

# 容器内配置路径 (通常不需要修改)
CONFIG_PATH=/app/config

# ============================================================================
# 定时任务配置 (分钟)
# ============================================================================

# 备份任务执行间隔 (默认: 1440分钟 = 24小时)
BACKUP_INTERVAL=1440

# 清理任务执行间隔 (默认: 1440分钟 = 24小时)
CLEANUP_INTERVAL=1440

# 监控任务执行间隔 (默认: 30分钟)
MONITOR_INTERVAL=30

# ============================================================================
# 备份策略配置
# ============================================================================

# 服务器文件保留天数 (默认: 7天)
DAYS_TO_KEEP=7

# 本地备份文件保留天数 (默认: 90天)
LOCAL_RETENTION_DAYS=90

# 磁盘使用率告警阈值 (默认: 85%)
DISK_USAGE_THRESHOLD=85

# ============================================================================
# 系统配置
# ============================================================================

# 时区设置
TZ=Asia/Shanghai

# 容器名称
CONTAINER_NAME=video-backup-system

# ============================================================================
# 高级配置 (可选)
# ============================================================================

# 邮件告警配置 (如果需要邮件通知)
# ALERT_EMAIL=<EMAIL>
# SMTP_SERVER=smtp.example.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=password

# 日志级别 (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL=INFO

# 并发任务数量限制
MAX_CONCURRENT_JOBS=3

# 网络超时设置 (秒)
NETWORK_TIMEOUT=300

# rclone传输配置
RCLONE_TRANSFERS=4
RCLONE_CHECKERS=8
RCLONE_BUFFER_SIZE=16M
