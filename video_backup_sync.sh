#!/bin/bash

# 视频备份同步脚本
# 功能：将MinIO服务器上超过7天的视频文件备份到本地，并从服务器删除
# 作者：自动生成
# 日期：$(date)

# 配置参数
RCLONE_CONFIG="./rclone.conf"
REMOTE_NAME="minio-remote"
LOCAL_BACKUP_PATH="/backup/video_archive"
BUCKET_NAME="records"
DAYS_TO_KEEP=7
LOG_FILE="/var/log/video_backup.log"
TEMP_LIST="/tmp/old_files_list.txt"

# 创建日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 创建备份目录
create_backup_dir() {
    if [ ! -d "$LOCAL_BACKUP_PATH" ]; then
        mkdir -p "$LOCAL_BACKUP_PATH"
        log_message "创建备份目录: $LOCAL_BACKUP_PATH"
    fi
}

# 获取设备列表
get_device_list() {
    log_message "获取设备列表..."
    rclone --config="$RCLONE_CONFIG" lsd "$REMOTE_NAME:$BUCKET_NAME" | awk '{print $5}' | grep -E '^device[0-9a-f]+$'
}

# 获取超过指定天数的目录
get_old_directories() {
    local device_id=$1
    local cutoff_date=$(date -d "$DAYS_TO_KEEP days ago" '+%Y-%m-%d')
    
    log_message "检查设备 $device_id 中超过 $DAYS_TO_KEEP 天的目录..."
    
    # 列出设备目录下的所有子目录
    rclone --config="$RCLONE_CONFIG" lsd "$REMOTE_NAME:$BUCKET_NAME/$device_id" | while read -r line; do
        dir_name=$(echo "$line" | awk '{print $5}')
        
        # 提取日期部分 (格式: 2025-01-23_20-46-05_hls)
        if [[ $dir_name =~ ^([0-9]{4}-[0-9]{2}-[0-9]{2})_.*_hls$ ]]; then
            dir_date="${BASH_REMATCH[1]}"
            
            # 比较日期
            if [[ "$dir_date" < "$cutoff_date" ]]; then
                echo "$device_id/$dir_name"
            fi
        fi
    done
}

# 备份单个目录
backup_directory() {
    local remote_path=$1
    local local_path="$LOCAL_BACKUP_PATH/$remote_path"
    
    log_message "开始备份: $remote_path"
    
    # 创建本地目录
    mkdir -p "$(dirname "$local_path")"
    
    # 使用rclone同步
    if rclone --config="$RCLONE_CONFIG" sync "$REMOTE_NAME:$BUCKET_NAME/$remote_path" "$local_path" --progress; then
        log_message "备份成功: $remote_path"
        return 0
    else
        log_message "备份失败: $remote_path"
        return 1
    fi
}

# 验证备份完整性
verify_backup() {
    local remote_path=$1
    local local_path="$LOCAL_BACKUP_PATH/$remote_path"
    
    log_message "验证备份完整性: $remote_path"
    
    # 比较文件数量和大小
    remote_size=$(rclone --config="$RCLONE_CONFIG" size "$REMOTE_NAME:$BUCKET_NAME/$remote_path" --json | jq -r '.bytes')
    local_size=$(rclone size "$local_path" --json | jq -r '.bytes')
    
    if [ "$remote_size" = "$local_size" ]; then
        log_message "备份验证成功: $remote_path (大小: $remote_size bytes)"
        return 0
    else
        log_message "备份验证失败: $remote_path (远程: $remote_size, 本地: $local_size)"
        return 1
    fi
}

# 删除远程目录
delete_remote_directory() {
    local remote_path=$1
    
    log_message "删除远程目录: $remote_path"
    
    if rclone --config="$RCLONE_CONFIG" purge "$REMOTE_NAME:$BUCKET_NAME/$remote_path"; then
        log_message "远程删除成功: $remote_path"
        return 0
    else
        log_message "远程删除失败: $remote_path"
        return 1
    fi
}

# 主函数
main() {
    log_message "========== 开始视频备份同步任务 =========="
    
    # 检查rclone配置
    if [ ! -f "$RCLONE_CONFIG" ]; then
        log_message "错误: rclone配置文件不存在: $RCLONE_CONFIG"
        exit 1
    fi
    
    # 创建备份目录
    create_backup_dir
    
    # 获取设备列表
    devices=$(get_device_list)
    
    if [ -z "$devices" ]; then
        log_message "警告: 未找到任何设备目录"
        exit 0
    fi
    
    # 处理每个设备
    for device in $devices; do
        log_message "处理设备: $device"
        
        # 获取需要备份的旧目录
        old_dirs=$(get_old_directories "$device")
        
        if [ -z "$old_dirs" ]; then
            log_message "设备 $device 没有需要备份的旧目录"
            continue
        fi
        
        # 备份每个旧目录
        echo "$old_dirs" | while read -r old_dir; do
            if [ -n "$old_dir" ]; then
                # 备份
                if backup_directory "$old_dir"; then
                    # 验证备份
                    if verify_backup "$old_dir"; then
                        # 删除远程文件
                        delete_remote_directory "$old_dir"
                    else
                        log_message "跳过删除远程目录，因为备份验证失败: $old_dir"
                    fi
                else
                    log_message "跳过删除远程目录，因为备份失败: $old_dir"
                fi
            fi
        done
    done
    
    log_message "========== 视频备份同步任务完成 =========="
}

# 错误处理
set -e
trap 'log_message "脚本执行出错，退出码: $?"' ERR

# 执行主函数
main "$@"
