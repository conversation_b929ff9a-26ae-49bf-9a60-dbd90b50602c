#!/bin/bash

# Video Backup System - rclone Configuration Script
# 视频备份系统 - rclone配置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否以root权限运行
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warn "建议不要以root权限运行此脚本"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 安装rclone
install_rclone() {
    log_info "检查rclone安装状态..."
    
    if command -v rclone &> /dev/null; then
        log_info "rclone已安装，版本: $(rclone version | head -n1)"
        return 0
    fi
    
    log_info "安装rclone..."
    curl https://rclone.org/install.sh | sudo bash
    
    if command -v rclone &> /dev/null; then
        log_info "rclone安装成功"
    else
        log_error "rclone安装失败"
        exit 1
    fi
}

# 读取MinIO配置
read_minio_config() {
    local config_file="config.json"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "找不到config.json文件"
        exit 1
    fi
    
    # 使用jq解析JSON，如果没有jq则手动解析
    if command -v jq &> /dev/null; then
        MINIO_URL=$(jq -r '.aliases.minio.url' "$config_file")
        MINIO_ACCESS_KEY=$(jq -r '.aliases.minio.accessKey' "$config_file")
        MINIO_SECRET_KEY=$(jq -r '.aliases.minio.secretKey' "$config_file")
    else
        log_warn "未安装jq，使用简单解析方法"
        MINIO_URL=$(grep -o '"url": *"[^"]*"' "$config_file" | cut -d'"' -f4)
        MINIO_ACCESS_KEY=$(grep -o '"accessKey": *"[^"]*"' "$config_file" | cut -d'"' -f4)
        MINIO_SECRET_KEY=$(grep -o '"secretKey": *"[^"]*"' "$config_file" | cut -d'"' -f4)
    fi
    
    log_info "MinIO配置读取成功"
    log_info "URL: $MINIO_URL"
    log_info "Access Key: ${MINIO_ACCESS_KEY:0:8}..."
}

# 配置rclone
configure_rclone() {
    log_info "配置rclone..."
    
    # 配置MinIO源
    log_info "配置MinIO源..."
    rclone config create minio-source s3 \
        provider=Minio \
        access_key_id="$MINIO_ACCESS_KEY" \
        secret_access_key="$MINIO_SECRET_KEY" \
        endpoint="$MINIO_URL" \
        --non-interactive
    
    # 获取本地备份路径
    read -p "请输入本地备份存储路径 [默认: /backup/videos]: " BACKUP_PATH
    BACKUP_PATH=${BACKUP_PATH:-/backup/videos}
    
    # 创建备份目录
    if [[ ! -d "$BACKUP_PATH" ]]; then
        log_info "创建备份目录: $BACKUP_PATH"
        sudo mkdir -p "$BACKUP_PATH"
        sudo chown $USER:$USER "$BACKUP_PATH"
    fi
    
    # 配置本地备份目标
    log_info "配置本地备份目标..."
    rclone config create local-backup local \
        --non-interactive
    
    # 测试连接
    log_info "测试MinIO连接..."
    if rclone lsd minio-source: &> /dev/null; then
        log_info "MinIO连接测试成功"
    else
        log_error "MinIO连接测试失败"
        exit 1
    fi
    
    # 保存配置到文件
    cat > backup-config.env << EOF
# Video Backup System Configuration
# 视频备份系统配置

# MinIO配置
MINIO_URL="$MINIO_URL"
MINIO_ACCESS_KEY="$MINIO_ACCESS_KEY"
MINIO_SECRET_KEY="$MINIO_SECRET_KEY"

# 备份配置
BACKUP_PATH="$BACKUP_PATH"
MINIO_SOURCE="minio-source:records"
LOCAL_BACKUP="$BACKUP_PATH"

# 日志配置
LOG_DIR="/var/log/video-backup"
LOG_FILE="\$LOG_DIR/backup.log"

# 保留策略
MINIO_RETENTION_DAYS=7
LOCAL_RETENTION_DAYS=30
CLOUD_RETENTION_DAYS=365

# 备份时间配置
BACKUP_HOUR=2
BACKUP_MINUTE=0

# 邮件告警配置（可选）
ALERT_EMAIL=""
SMTP_SERVER=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
EOF
    
    log_info "配置文件已保存到 backup-config.env"
}

# 主函数
main() {
    log_info "开始配置视频备份系统的rclone..."
    
    check_root
    install_rclone
    read_minio_config
    configure_rclone
    
    log_info "rclone配置完成！"
    log_info "下一步请运行主备份脚本进行测试"
}

# 运行主函数
main "$@"
