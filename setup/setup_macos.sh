#!/bin/bash

# macOS视频备份系统安装配置脚本
# 功能：为macOS系统配置视频备份环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为macOS
check_macos() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        log_error "此脚本仅适用于macOS系统"
        exit 1
    fi
    log_info "检测到macOS系统"
}

# 检查并安装Homebrew
install_homebrew() {
    if ! command -v brew &> /dev/null; then
        log_info "安装Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    else
        log_info "Homebrew已安装"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装依赖软件..."
    
    # 安装rclone
    if ! command -v rclone &> /dev/null; then
        log_info "安装rclone..."
        brew install rclone
    else
        log_info "rclone已安装"
    fi
    
    # 安装jq
    if ! command -v jq &> /dev/null; then
        log_info "安装jq..."
        brew install jq
    else
        log_info "jq已安装"
    fi
}

# 创建日志目录
create_log_directories() {
    local log_dir="$HOME/logs"
    
    if [[ ! -d "$log_dir" ]]; then
        log_info "创建日志目录: $log_dir"
        mkdir -p "$log_dir"
    fi
}

# 修改脚本中的日志路径
update_log_paths() {
    log_info "更新脚本中的日志路径为macOS兼容路径..."
    
    local scripts=("video_backup_sync.sh" "video_cleanup.sh" "monitor_system.sh")
    local log_dir="$HOME/logs"
    
    for script in "${scripts[@]}"; do
        if [[ -f "$script" ]]; then
            log_info "更新 $script 中的日志路径"
            # 备份原文件
            cp "$script" "${script}.backup"
            
            # 替换日志路径
            sed -i '' "s|/var/log/video_backup.log|${log_dir}/video_backup.log|g" "$script"
            sed -i '' "s|/var/log/video_cleanup.log|${log_dir}/video_cleanup.log|g" "$script"
            sed -i '' "s|/var/log/video_monitor.log|${log_dir}/video_monitor.log|g" "$script"
            sed -i '' "s|/var/log/video_backup_cron.log|${log_dir}/video_backup_cron.log|g" "$script"
            sed -i '' "s|/var/log/video_cleanup_cron.log|${log_dir}/video_cleanup_cron.log|g" "$script"
        fi
    done
}

# 创建launchd配置文件
create_launchd_configs() {
    log_info "创建launchd定时任务配置..."
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local project_dir="$(dirname "$script_dir")"
    local plist_dir="$HOME/Library/LaunchAgents"
    
    # 确保目录存在
    mkdir -p "$plist_dir"
    
    # 备份任务 - 每天凌晨2点
    cat > "$plist_dir/com.videobackup.sync.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.videobackup.sync</string>
    <key>ProgramArguments</key>
    <array>
        <string>$project_dir/scripts/video_backup_sync.sh</string>
    </array>
    <key>StartCalendarInterval</key>
    <dict>
        <key>Hour</key>
        <integer>2</integer>
        <key>Minute</key>
        <integer>0</integer>
    </dict>
    <key>StandardOutPath</key>
    <string>$HOME/logs/video_backup_cron.log</string>
    <key>StandardErrorPath</key>
    <string>$HOME/logs/video_backup_cron.log</string>
</dict>
</plist>
EOF

    # 清理任务 - 每天凌晨1点
    cat > "$plist_dir/com.videobackup.cleanup.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.videobackup.cleanup</string>
    <key>ProgramArguments</key>
    <array>
        <string>$project_dir/scripts/video_cleanup.sh</string>
        <string>remote</string>
    </array>
    <key>StartCalendarInterval</key>
    <dict>
        <key>Hour</key>
        <integer>1</integer>
        <key>Minute</key>
        <integer>0</integer>
    </dict>
    <key>StandardOutPath</key>
    <string>$HOME/logs/video_cleanup_cron.log</string>
    <key>StandardErrorPath</key>
    <string>$HOME/logs/video_cleanup_cron.log</string>
</dict>
</plist>
EOF

    # 本地清理任务 - 每周日凌晨3点
    cat > "$plist_dir/com.videobackup.localcleanup.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.videobackup.localcleanup</string>
    <key>ProgramArguments</key>
    <array>
        <string>$project_dir/scripts/video_cleanup.sh</string>
        <string>local</string>
    </array>
    <key>StartCalendarInterval</key>
    <dict>
        <key>Hour</key>
        <integer>3</integer>
        <key>Minute</key>
        <integer>0</integer>
        <key>Weekday</key>
        <integer>0</integer>
    </dict>
    <key>StandardOutPath</key>
    <string>$HOME/logs/video_cleanup_cron.log</string>
    <key>StandardErrorPath</key>
    <string>$HOME/logs/video_cleanup_cron.log</string>
</dict>
</plist>
EOF

    log_info "launchd配置文件已创建"
}

# 加载launchd任务
load_launchd_tasks() {
    log_info "加载launchd定时任务..."
    
    local plist_dir="$HOME/Library/LaunchAgents"
    local plists=("com.videobackup.sync.plist" "com.videobackup.cleanup.plist" "com.videobackup.localcleanup.plist")
    
    for plist in "${plists[@]}"; do
        if [[ -f "$plist_dir/$plist" ]]; then
            launchctl load "$plist_dir/$plist"
            log_info "已加载: $plist"
        fi
    done
}

# 测试连接
test_connection() {
    log_info "测试rclone连接..."
    
    if rclone --config=./rclone.conf lsd minio-remote:records &> /dev/null; then
        log_info "✅ rclone连接测试成功"
    else
        log_warn "❌ rclone连接测试失败，请检查配置"
    fi
}

# 显示使用说明
show_usage_info() {
    log_info "========== macOS安装完成 =========="
    echo
    echo "📁 日志目录: $HOME/logs"
    echo "⏰ 定时任务已配置:"
    echo "   - 每天凌晨1点: 清理远程过期文件"
    echo "   - 每天凌晨2点: 备份同步"
    echo "   - 每周日凌晨3点: 清理本地过期文件"
    echo
    echo "🔧 管理命令:"
    echo "   查看定时任务: launchctl list | grep videobackup"
    echo "   停止任务: launchctl unload ~/Library/LaunchAgents/com.videobackup.*.plist"
    echo "   启动任务: launchctl load ~/Library/LaunchAgents/com.videobackup.*.plist"
    echo "   查看日志: tail -f ~/logs/video_backup.log"
    echo
    echo "🧪 测试命令:"
    echo "   手动备份: ./video_backup_sync.sh"
    echo "   系统监控: ./monitor_system.sh check"
    echo "   清理测试: ./video_cleanup.sh local"
    echo
}

# 主函数
main() {
    log_info "开始macOS视频备份系统配置..."
    
    check_macos
    install_homebrew
    install_dependencies
    create_log_directories
    update_log_paths
    create_launchd_configs
    load_launchd_tasks
    test_connection
    show_usage_info
    
    log_info "macOS配置完成！"
}

# 显示帮助
show_help() {
    cat << EOF
macOS视频备份系统安装脚本

用法: $0 [选项]

选项:
    -h, --help      显示帮助信息
    --uninstall     卸载定时任务

说明:
    本脚本将为macOS系统配置视频备份环境，包括:
    1. 安装Homebrew和依赖软件
    2. 调整日志路径为用户目录
    3. 创建launchd定时任务
    4. 测试系统连接

EOF
}

# 卸载函数
uninstall() {
    log_info "卸载launchd定时任务..."
    
    local plist_dir="$HOME/Library/LaunchAgents"
    local plists=("com.videobackup.sync.plist" "com.videobackup.cleanup.plist" "com.videobackup.localcleanup.plist")
    
    for plist in "${plists[@]}"; do
        if [[ -f "$plist_dir/$plist" ]]; then
            launchctl unload "$plist_dir/$plist" 2>/dev/null || true
            rm "$plist_dir/$plist"
            log_info "已删除: $plist"
        fi
    done
    
    log_info "卸载完成"
}

# 参数处理
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    --uninstall)
        uninstall
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
