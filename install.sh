#!/bin/bash

# Video Backup System - Installation Script
# 视频备份系统 - 安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 系统信息
SYSTEM_USER="${SUDO_USER:-$USER}"
INSTALL_DIR="/opt/video-backup"
SERVICE_DIR="/etc/systemd/system"
LOG_DIR="/var/log/video-backup"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "不支持的操作系统"
        exit 1
    fi
    
    # 检查是否为root用户
    if [[ $EUID -ne 0 ]]; then
        log_error "请以root权限运行此脚本"
        exit 1
    fi
    
    # 检查必要命令
    local required_commands=("curl" "systemctl" "crontab")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log_error "缺少必要命令: $cmd"
            exit 1
        fi
    done
    
    log_info "系统要求检查通过"
}

# 安装依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    # 检测包管理器并安装依赖
    if command -v apt-get &> /dev/null; then
        apt-get update
        apt-get install -y curl wget jq mailutils cron
    elif command -v yum &> /dev/null; then
        yum update -y
        yum install -y curl wget jq mailx cronie
        systemctl enable crond
        systemctl start crond
    elif command -v dnf &> /dev/null; then
        dnf update -y
        dnf install -y curl wget jq mailx cronie
        systemctl enable crond
        systemctl start crond
    else
        log_warn "未识别的包管理器，请手动安装: curl wget jq mailutils cron"
    fi
    
    log_info "依赖安装完成"
}

# 安装rclone
install_rclone() {
    log_info "安装rclone..."
    
    if command -v rclone &> /dev/null; then
        log_info "rclone已安装，版本: $(rclone version | head -n1)"
        return 0
    fi
    
    curl https://rclone.org/install.sh | bash
    
    if command -v rclone &> /dev/null; then
        log_info "rclone安装成功"
    else
        log_error "rclone安装失败"
        exit 1
    fi
}

# 创建系统用户和目录
setup_system() {
    log_info "设置系统环境..."
    
    # 创建安装目录
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$LOG_DIR"
    
    # 设置权限
    chown -R "$SYSTEM_USER:$SYSTEM_USER" "$INSTALL_DIR"
    chown -R "$SYSTEM_USER:$SYSTEM_USER" "$LOG_DIR"
    
    log_info "系统环境设置完成"
}

# 复制脚本文件
install_scripts() {
    log_info "安装脚本文件..."
    
    # 复制脚本文件到安装目录
    local script_files=(
        "setup-rclone.sh"
        "video-backup.sh"
        "backup-monitor.sh"
        "setup-minio-lifecycle.sh"
        "config.json"
    )
    
    for file in "${script_files[@]}"; do
        if [[ -f "$file" ]]; then
            cp "$file" "$INSTALL_DIR/"
            chown "$SYSTEM_USER:$SYSTEM_USER" "$INSTALL_DIR/$file"
            
            # 设置脚本执行权限
            if [[ "$file" == *.sh ]]; then
                chmod +x "$INSTALL_DIR/$file"
            fi
            
            log_info "已安装: $file"
        else
            log_warn "文件不存在: $file"
        fi
    done
}

# 创建systemd服务
create_systemd_service() {
    log_info "创建systemd服务..."
    
    # 创建备份服务
    cat > "$SERVICE_DIR/video-backup.service" << EOF
[Unit]
Description=Video Backup Service
After=network.target

[Service]
Type=oneshot
User=$SYSTEM_USER
Group=$SYSTEM_USER
WorkingDirectory=$INSTALL_DIR
ExecStart=$INSTALL_DIR/video-backup.sh
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # 创建备份定时器
    cat > "$SERVICE_DIR/video-backup.timer" << EOF
[Unit]
Description=Video Backup Timer
Requires=video-backup.service

[Timer]
OnCalendar=daily
Persistent=true
RandomizedDelaySec=300

[Install]
WantedBy=timers.target
EOF
    
    # 创建监控服务
    cat > "$SERVICE_DIR/video-backup-monitor.service" << EOF
[Unit]
Description=Video Backup Monitor Service
After=network.target

[Service]
Type=oneshot
User=$SYSTEM_USER
Group=$SYSTEM_USER
WorkingDirectory=$INSTALL_DIR
ExecStart=$INSTALL_DIR/backup-monitor.sh --check
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # 创建监控定时器
    cat > "$SERVICE_DIR/video-backup-monitor.timer" << EOF
[Unit]
Description=Video Backup Monitor Timer
Requires=video-backup-monitor.service

[Timer]
OnCalendar=*:0/30
Persistent=true

[Install]
WantedBy=timers.target
EOF
    
    # 重新加载systemd
    systemctl daemon-reload
    
    log_info "systemd服务创建完成"
}

# 设置cron任务
setup_cron() {
    log_info "设置cron任务..."
    
    # 为系统用户添加cron任务
    local cron_content="# Video Backup System Cron Jobs
# 每天凌晨2点执行备份
0 2 * * * $INSTALL_DIR/video-backup.sh >> $LOG_DIR/cron.log 2>&1

# 每天凌晨3点执行rclone清理（在备份之后）
0 3 * * * $INSTALL_DIR/rclone-cleanup.sh >> $LOG_DIR/cleanup-cron.log 2>&1

# 每30分钟执行监控检查
*/30 * * * * $INSTALL_DIR/backup-monitor.sh --check >> $LOG_DIR/monitor-cron.log 2>&1

# 每天早上8点生成日报
0 8 * * * $INSTALL_DIR/backup-monitor.sh --report >> $LOG_DIR/report-cron.log 2>&1

# 每周日凌晨4点清理日志
0 4 * * 0 $INSTALL_DIR/backup-monitor.sh --cleanup >> $LOG_DIR/cleanup-cron.log 2>&1
"
    
    # 安装cron任务
    echo "$cron_content" | sudo -u "$SYSTEM_USER" crontab -
    
    log_info "cron任务设置完成"
}

# 创建管理脚本
create_management_script() {
    log_info "创建管理脚本..."
    
    cat > "$INSTALL_DIR/manage.sh" << 'EOF'
#!/bin/bash

# Video Backup System Management Script
# 视频备份系统管理脚本

INSTALL_DIR="/opt/video-backup"
cd "$INSTALL_DIR"

show_help() {
    cat << HELP
视频备份系统管理工具

用法: $0 [命令]

命令:
    status          显示系统状态
    start           启动备份服务
    stop            停止备份服务
    restart         重启备份服务
    backup          立即执行备份
    monitor         显示监控状态
    logs            查看日志
    config          重新配置系统
    test            测试系统功能
    help            显示此帮助

示例:
    $0 status       # 查看状态
    $0 backup       # 立即备份
    $0 logs         # 查看日志

HELP
}

case "${1:-help}" in
    status)
        echo "=== 系统服务状态 ==="
        systemctl status video-backup.timer video-backup-monitor.timer
        echo
        echo "=== 备份状态 ==="
        ./backup-monitor.sh --status
        ;;
    start)
        systemctl enable video-backup.timer video-backup-monitor.timer
        systemctl start video-backup.timer video-backup-monitor.timer
        echo "备份服务已启动"
        ;;
    stop)
        systemctl stop video-backup.timer video-backup-monitor.timer
        systemctl disable video-backup.timer video-backup-monitor.timer
        echo "备份服务已停止"
        ;;
    restart)
        systemctl restart video-backup.timer video-backup-monitor.timer
        echo "备份服务已重启"
        ;;
    backup)
        echo "开始手动备份..."
        ./video-backup.sh
        ;;
    monitor)
        ./backup-monitor.sh --status
        ;;
    logs)
        echo "=== 最近的备份日志 ==="
        tail -50 /var/log/video-backup/backup.log 2>/dev/null || echo "无日志文件"
        echo
        echo "=== 最近的监控日志 ==="
        tail -20 /var/log/video-backup/monitor.log 2>/dev/null || echo "无日志文件"
        ;;
    config)
        echo "重新配置系统..."
        ./setup-rclone.sh
        ;;
    test)
        echo "测试系统功能..."
        ./video-backup.sh --test
        ./backup-monitor.sh --test-alert
        ;;
    help|*)
        show_help
        ;;
esac
EOF
    
    chmod +x "$INSTALL_DIR/manage.sh"
    chown "$SYSTEM_USER:$SYSTEM_USER" "$INSTALL_DIR/manage.sh"
    
    # 创建全局命令链接
    ln -sf "$INSTALL_DIR/manage.sh" /usr/local/bin/video-backup
    
    log_info "管理脚本创建完成"
}

# 显示安装完成信息
show_completion_info() {
    log_info "安装完成！"
    echo
    echo "=== 下一步操作 ==="
    echo "1. 配置rclone连接:"
    echo "   cd $INSTALL_DIR && sudo -u $SYSTEM_USER ./setup-rclone.sh"
    echo
    echo "2. 配置rclone清理策略:"
    echo "   cd $INSTALL_DIR && sudo -u $SYSTEM_USER ./setup-minio-lifecycle.sh"
    echo
    echo "3. 启动备份服务:"
    echo "   video-backup start"
    echo
    echo "4. 查看系统状态:"
    echo "   video-backup status"
    echo
    echo "=== 管理命令 ==="
    echo "使用 'video-backup' 命令管理系统"
    echo "使用 'video-backup help' 查看所有可用命令"
    echo
    echo "=== 重要文件位置 ==="
    echo "安装目录: $INSTALL_DIR"
    echo "日志目录: $LOG_DIR"
    echo "配置文件: $INSTALL_DIR/backup-config.env"
    echo
}

# 主安装函数
main() {
    log_info "开始安装视频备份系统..."
    
    check_requirements
    install_dependencies
    install_rclone
    setup_system
    install_scripts
    create_systemd_service
    setup_cron
    create_management_script
    
    show_completion_info
}

# 运行安装
main "$@"
