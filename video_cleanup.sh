#!/bin/bash

# 视频清理脚本
# 功能：清理本地备份中的过期文件，可选择性清理远程服务器上的过期文件
# 作者：自动生成
# 日期：$(date)

# 配置参数
RCLONE_CONFIG="./rclone.conf"
REMOTE_NAME="minio-remote"
LOCAL_BACKUP_PATH="/backup/video_archive"
BUCKET_NAME="records"
LOCAL_RETENTION_DAYS=90  # 本地保留90天
REMOTE_RETENTION_DAYS=7  # 远程保留7天
LOG_FILE="/var/log/video_cleanup.log"

# 创建日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 清理本地过期文件
cleanup_local_files() {
    local retention_days=$1
    local cutoff_date=$(date -d "$retention_days days ago" '+%Y-%m-%d')
    
    log_message "开始清理本地超过 $retention_days 天的文件..."
    
    if [ ! -d "$LOCAL_BACKUP_PATH" ]; then
        log_message "本地备份目录不存在: $LOCAL_BACKUP_PATH"
        return 0
    fi
    
    # 查找并删除过期目录
    find "$LOCAL_BACKUP_PATH" -type d -name "*_hls" | while read -r dir; do
        # 提取目录名中的日期
        dir_name=$(basename "$dir")
        if [[ $dir_name =~ ^([0-9]{4}-[0-9]{2}-[0-9]{2})_.*_hls$ ]]; then
            dir_date="${BASH_REMATCH[1]}"
            
            # 比较日期
            if [[ "$dir_date" < "$cutoff_date" ]]; then
                log_message "删除本地过期目录: $dir"
                rm -rf "$dir"
                
                # 检查父目录是否为空，如果为空则删除
                parent_dir=$(dirname "$dir")
                if [ -d "$parent_dir" ] && [ -z "$(ls -A "$parent_dir")" ]; then
                    log_message "删除空的设备目录: $parent_dir"
                    rmdir "$parent_dir"
                fi
            fi
        fi
    done
}

# 清理远程过期文件（可选）
cleanup_remote_files() {
    local retention_days=$1
    local cutoff_date=$(date -d "$retention_days days ago" '+%Y-%m-%d')
    
    log_message "开始清理远程超过 $retention_days 天的文件..."
    
    # 获取设备列表
    devices=$(rclone --config="$RCLONE_CONFIG" lsd "$REMOTE_NAME:$BUCKET_NAME" | awk '{print $5}' | grep -E '^device[0-9a-f]+$')
    
    for device in $devices; do
        log_message "检查设备: $device"
        
        # 列出设备目录下的所有子目录
        rclone --config="$RCLONE_CONFIG" lsd "$REMOTE_NAME:$BUCKET_NAME/$device" | while read -r line; do
            dir_name=$(echo "$line" | awk '{print $5}')
            
            # 提取日期部分
            if [[ $dir_name =~ ^([0-9]{4}-[0-9]{2}-[0-9]{2})_.*_hls$ ]]; then
                dir_date="${BASH_REMATCH[1]}"
                
                # 比较日期
                if [[ "$dir_date" < "$cutoff_date" ]]; then
                    log_message "删除远程过期目录: $device/$dir_name"
                    rclone --config="$RCLONE_CONFIG" purge "$REMOTE_NAME:$BUCKET_NAME/$device/$dir_name"
                fi
            fi
        done
    done
}

# 生成清理报告
generate_cleanup_report() {
    log_message "========== 清理报告 =========="
    
    # 本地存储使用情况
    if [ -d "$LOCAL_BACKUP_PATH" ]; then
        local_usage=$(du -sh "$LOCAL_BACKUP_PATH" 2>/dev/null | cut -f1)
        local_file_count=$(find "$LOCAL_BACKUP_PATH" -type f | wc -l)
        log_message "本地备份存储使用: $local_usage"
        log_message "本地备份文件数量: $local_file_count"
    fi
    
    # 远程存储使用情况
    remote_size=$(rclone --config="$RCLONE_CONFIG" size "$REMOTE_NAME:$BUCKET_NAME" --json 2>/dev/null | jq -r '.bytes // 0')
    if [ "$remote_size" != "0" ]; then
        remote_size_human=$(numfmt --to=iec "$remote_size")
        log_message "远程存储使用: $remote_size_human"
    fi
    
    log_message "========== 清理报告结束 =========="
}

# 主函数
main() {
    local mode=${1:-"local"}  # 默认只清理本地文件
    
    log_message "========== 开始视频清理任务 (模式: $mode) =========="
    
    case $mode in
        "local")
            cleanup_local_files $LOCAL_RETENTION_DAYS
            ;;
        "remote")
            cleanup_remote_files $REMOTE_RETENTION_DAYS
            ;;
        "both")
            cleanup_local_files $LOCAL_RETENTION_DAYS
            cleanup_remote_files $REMOTE_RETENTION_DAYS
            ;;
        *)
            log_message "错误: 未知的清理模式: $mode"
            log_message "支持的模式: local, remote, both"
            exit 1
            ;;
    esac
    
    generate_cleanup_report
    
    log_message "========== 视频清理任务完成 =========="
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 [模式]"
    echo "模式选项:"
    echo "  local  - 只清理本地备份文件 (默认)"
    echo "  remote - 只清理远程服务器文件"
    echo "  both   - 清理本地和远程文件"
    echo ""
    echo "配置:"
    echo "  本地文件保留天数: $LOCAL_RETENTION_DAYS 天"
    echo "  远程文件保留天数: $REMOTE_RETENTION_DAYS 天"
}

# 参数处理
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
fi

# 错误处理
set -e
trap 'log_message "清理脚本执行出错，退出码: $?"' ERR

# 执行主函数
main "$@"
